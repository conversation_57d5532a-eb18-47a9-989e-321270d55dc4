"""
Debug Interface for H-Bridge Circuit Analysis
Allows direct testing and debugging of the circuit system.
"""

import tkinter as tk
from tkinter import ttk, messagebox
import sys
import os

# Add current directory to path for imports
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from circuit_model import CircuitModel, ParameterState, MeterType
from electrical_calculator import ElectricalCalculator


class DebugInterface:
    """Debug interface for testing circuit calculations"""
    
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("H-Bridge Debug Interface")
        self.root.geometry("1200x800")
        
        # Initialize model and calculator
        self.model = CircuitModel()
        self.calculator = ElectricalCalculator()
        
        self.setup_ui()
        
    def setup_ui(self):
        """Setup the debug interface"""
        # Main notebook for tabs
        notebook = ttk.Notebook(self.root)
        notebook.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # Quick Test Tab
        self.setup_quick_test_tab(notebook)
        
        # Manual Entry Tab
        self.setup_manual_entry_tab(notebook)
        
        # Debug Output Tab
        self.setup_debug_output_tab(notebook)
        
    def setup_quick_test_tab(self, notebook):
        """Setup quick test scenarios"""
        frame = ttk.Frame(notebook)
        notebook.add(frame, text="Quick Tests")
        
        # Test scenarios
        ttk.Label(frame, text="Quick Test Scenarios", font=('Arial', 14, 'bold')).pack(pady=10)
        
        # User's scenario
        user_frame = ttk.LabelFrame(frame, text="User's Scenario: R6 Current + Voltage → Calculate R3")
        user_frame.pack(fill=tk.X, padx=10, pady=5)
        
        ttk.Button(user_frame, text="Test: R6 = -559μA, 0.1V → Calculate R3", 
                  command=self.test_user_scenario).pack(pady=5)
        
        # Other test scenarios
        test_frame = ttk.LabelFrame(frame, text="Other Test Scenarios")
        test_frame.pack(fill=tk.X, padx=10, pady=5)
        
        ttk.Button(test_frame, text="Test: Basic Ohm's Law", 
                  command=self.test_ohms_law).pack(pady=2)
        ttk.Button(test_frame, text="Test: Bridge Analysis", 
                  command=self.test_bridge_analysis).pack(pady=2)
        ttk.Button(test_frame, text="Test: Unit Conversion", 
                  command=self.test_unit_conversion).pack(pady=2)
        
        # Results area
        self.results_text = tk.Text(frame, height=20, wrap=tk.WORD)
        scrollbar = ttk.Scrollbar(frame, orient=tk.VERTICAL, command=self.results_text.yview)
        self.results_text.configure(yscrollcommand=scrollbar.set)
        
        self.results_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(10, 0), pady=10)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y, pady=10)
        
    def setup_manual_entry_tab(self, notebook):
        """Setup manual parameter entry"""
        frame = ttk.Frame(notebook)
        notebook.add(frame, text="Manual Entry")
        
        ttk.Label(frame, text="Manual Parameter Entry", font=('Arial', 14, 'bold')).pack(pady=10)
        
        # Create entry widgets for each resistor
        self.entry_widgets = {}
        
        for resistor_name in ['R1', 'R2', 'R3', 'R4', 'R5', 'R6']:
            resistor_frame = ttk.LabelFrame(frame, text=f"{resistor_name} Parameters")
            resistor_frame.pack(fill=tk.X, padx=10, pady=5)
            
            # Create entry fields for each parameter
            self.entry_widgets[resistor_name] = {}
            
            for param in ['resistance', 'voltage', 'current', 'power']:
                param_frame = ttk.Frame(resistor_frame)
                param_frame.pack(fill=tk.X, padx=5, pady=2)
                
                ttk.Label(param_frame, text=f"{param.title()}:", width=12).pack(side=tk.LEFT)
                
                value_entry = ttk.Entry(param_frame, width=10)
                value_entry.pack(side=tk.LEFT, padx=5)
                
                unit_combo = ttk.Combobox(param_frame, width=8)
                if param == 'resistance':
                    unit_combo['values'] = ['Ω', 'kΩ', 'MΩ']
                elif param == 'voltage':
                    unit_combo['values'] = ['V', 'mV', 'μV']
                elif param == 'current':
                    unit_combo['values'] = ['A', 'mA', 'μA', 'nA']
                elif param == 'power':
                    unit_combo['values'] = ['W', 'mW', 'μW']
                unit_combo.pack(side=tk.LEFT, padx=5)
                
                state_combo = ttk.Combobox(param_frame, width=10)
                state_combo['values'] = ['INACTIVE', 'GIVEN', 'UNKNOWN']
                state_combo.pack(side=tk.LEFT, padx=5)
                
                self.entry_widgets[resistor_name][param] = {
                    'value': value_entry,
                    'unit': unit_combo,
                    'state': state_combo
                }
        
        # Control buttons
        button_frame = ttk.Frame(frame)
        button_frame.pack(fill=tk.X, padx=10, pady=10)
        
        ttk.Button(button_frame, text="Apply Values", command=self.apply_manual_values).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="Calculate", command=self.calculate_manual).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="Clear All", command=self.clear_manual).pack(side=tk.LEFT, padx=5)
        
    def setup_debug_output_tab(self, notebook):
        """Setup debug output display"""
        frame = ttk.Frame(notebook)
        notebook.add(frame, text="Debug Output")
        
        ttk.Label(frame, text="Debug Output", font=('Arial', 14, 'bold')).pack(pady=10)
        
        self.debug_text = tk.Text(frame, height=30, wrap=tk.WORD, font=('Consolas', 10))
        debug_scrollbar = ttk.Scrollbar(frame, orient=tk.VERTICAL, command=self.debug_text.yview)
        self.debug_text.configure(yscrollcommand=debug_scrollbar.set)
        
        self.debug_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(10, 0), pady=10)
        debug_scrollbar.pack(side=tk.RIGHT, fill=tk.Y, pady=10)
        
    def test_user_scenario(self):
        """Test the user's specific scenario"""
        self.log("🎯 TESTING USER'S SCENARIO")
        self.log("Setting up: R6 current = -559μA, R6 voltage = 0.1V, Calculate R3")
        
        # Reset model
        self.model = CircuitModel()
        
        # Set R6 parameters
        r6 = self.model.get_resistor('R6')
        r6.current.value = -559
        r6.current.unit = 'μA'
        r6.current.state = ParameterState.GIVEN
        
        r6.voltage.value = 0.1
        r6.voltage.unit = 'V'
        r6.voltage.state = ParameterState.GIVEN
        
        # Set R3 to unknown
        r3 = self.model.get_resistor('R3')
        r3.resistance.state = ParameterState.UNKNOWN
        
        # Set meter to ammeter with the current value
        self.model.meter_type = MeterType.AMMETER
        self.model.meter_measurement.value = -559
        self.model.meter_measurement.state = ParameterState.GIVEN
        
        self.log("✅ Parameters set up")
        self.log(f"R6 current: {r6.current.value} {r6.current.unit} (state: {r6.current.state.name})")
        self.log(f"R6 voltage: {r6.voltage.value} {r6.voltage.unit} (state: {r6.voltage.state.name})")
        self.log(f"R3 resistance: {r3.resistance.value} {r3.resistance.unit} (state: {r3.resistance.state.name})")
        
        # Calculate
        try:
            results = self.calculator.solve_circuit(self.model)
            self.log("✅ CALCULATION COMPLETED")
            self.display_results(results)
        except Exception as e:
            self.log(f"❌ CALCULATION FAILED: {e}")
            import traceback
            self.log(traceback.format_exc())
    
    def test_ohms_law(self):
        """Test basic Ohm's law calculation"""
        self.log("🔧 TESTING BASIC OHM'S LAW")
        # Implementation here
        
    def test_bridge_analysis(self):
        """Test bridge analysis"""
        self.log("🌉 TESTING BRIDGE ANALYSIS")
        # Implementation here
        
    def test_unit_conversion(self):
        """Test unit conversion"""
        self.log("🔄 TESTING UNIT CONVERSION")
        # Test μA to A conversion
        converter = self.calculator.converter
        
        test_values = [
            (-559, 'μA', 'current'),
            (0.1, 'V', 'voltage'),
            (1000, 'mV', 'voltage'),
            (2.2, 'kΩ', 'resistance')
        ]
        
        for value, unit, param_type in test_values:
            base_value = converter.to_base_unit(value, unit, param_type)
            self.log(f"Convert: {value} {unit} → {base_value} (base unit)")
    
    def apply_manual_values(self):
        """Apply manually entered values to the model"""
        self.log("📝 APPLYING MANUAL VALUES")
        # Implementation here
        
    def calculate_manual(self):
        """Calculate with manually entered values"""
        self.log("🧮 CALCULATING WITH MANUAL VALUES")
        # Implementation here
        
    def clear_manual(self):
        """Clear all manual entries"""
        self.log("🗑️ CLEARING MANUAL ENTRIES")
        # Implementation here
    
    def log(self, message):
        """Log message to results area"""
        self.results_text.insert(tk.END, message + "\n")
        self.results_text.see(tk.END)
        self.root.update()
        
        # Also log to debug output
        self.debug_text.insert(tk.END, message + "\n")
        self.debug_text.see(tk.END)
        
    def display_results(self, results):
        """Display calculation results"""
        self.log("\n📊 CALCULATION RESULTS:")
        self.log("="*50)
        
        for component, params in results.items():
            self.log(f"\n🔧 {component}:")
            for param, value in params.items():
                self.log(f"   {param}: {value}")
    
    def run(self):
        """Start the debug interface"""
        self.root.mainloop()


def main():
    """Main entry point for debug interface"""
    try:
        debug_app = DebugInterface()
        debug_app.run()
    except Exception as e:
        print(f"Error starting debug interface: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
