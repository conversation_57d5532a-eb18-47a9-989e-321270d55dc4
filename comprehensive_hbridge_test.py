"""
Comprehensive H-Bridge Circuit Calculation Test Suite
Tests ALL possible scenarios and parameter combinations
"""

import sys
import os
import itertools
from typing import Dict, List, Tuple, Optional

sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from circuit_model import CircuitModel, ParameterState, MeterType
from electrical_calculator import ElectricalCalculator, UnitConverter


class ComprehensiveHBridgeTest:
    """Comprehensive testing of all H-bridge calculation scenarios"""
    
    def __init__(self):
        self.test_results = []
        self.passed_tests = 0
        self.failed_tests = 0
        self.calculator = ElectricalCalculator()
        
    def log_test(self, test_name: str, passed: bool, details: str = "", expected: str = "", actual: str = ""):
        """Log test result with detailed information"""
        status = "✅ PASS" if passed else "❌ FAIL"
        result_entry = {
            'name': test_name,
            'passed': passed,
            'details': details,
            'expected': expected,
            'actual': actual
        }
        self.test_results.append(result_entry)
        
        if passed:
            self.passed_tests += 1
            print(f"{status} {test_name}")
            if details:
                print(f"    {details}")
        else:
            self.failed_tests += 1
            print(f"{status} {test_name}")
            if details:
                print(f"    {details}")
            if expected and actual:
                print(f"    Expected: {expected}")
                print(f"    Actual: {actual}")
    
    def test_primary_scenario(self):
        """Test the primary scenario from the user's reference image"""
        print("\n🎯 TESTING PRIMARY SCENARIO (User's Reference)")
        print("="*60)
        
        model = CircuitModel()
        
        # Set up the exact scenario from the reference image
        # R1 = 180Ω (enabled)
        r1 = model.get_resistor('R1')
        r1.resistance.value = 180
        r1.resistance.unit = 'Ω'
        r1.resistance.state = ParameterState.GIVEN
        r1.enabled = True
        
        # R2 = 100Ω (H-bridge component, always enabled)  
        r2 = model.get_resistor('R2')
        r2.resistance.value = 100
        r2.resistance.unit = 'Ω'
        r2.resistance.state = ParameterState.GIVEN
        
        # R3 = UNKNOWN (to be calculated)
        r3 = model.get_resistor('R3')
        r3.resistance.state = ParameterState.UNKNOWN
        r3.resistance.value = 0.0
        
        # R4 = 22Ω (H-bridge component, always enabled)
        r4 = model.get_resistor('R4')
        r4.resistance.value = 22
        r4.resistance.unit = 'Ω'
        r4.resistance.state = ParameterState.GIVEN
        
        # R5 = 39Ω (H-bridge component, always enabled)
        r5 = model.get_resistor('R5')
        r5.resistance.value = 39
        r5.resistance.unit = 'Ω'
        r5.resistance.state = ParameterState.GIVEN
        
        # R6 voltage = 0.1V (user measured)
        r6 = model.get_resistor('R6')
        r6.voltage.value = 0.1
        r6.voltage.unit = 'V'
        r6.voltage.state = ParameterState.GIVEN
        r6.enabled = True
        
        # R6 current = -559μA (user measured)
        r6.current.value = -559
        r6.current.unit = 'μA'
        r6.current.state = ParameterState.GIVEN
        
        # Supply voltage = 1.0V
        model.supply_voltage.value = 1.0
        model.supply_voltage.unit = 'V'
        model.supply_voltage.state = ParameterState.GIVEN
        
        # Meter type = Ammeter, reading = -559μA
        model.meter_type = MeterType.AMMETER
        model.meter_measurement.value = -559
        model.meter_measurement.unit = 'μA'
        model.meter_measurement.state = ParameterState.GIVEN
        
        try:
            # Calculate
            results = self.calculator.solve_circuit(model)
            
            # Check if R3 was calculated
            if 'R3' in results and 'resistance' in results['R3']:
                r3_calculated = results['R3']['resistance']
                expected_r3 = 283.65  # From reference image calculation
                tolerance = 1.0  # 1 ohm tolerance
                
                if abs(r3_calculated - expected_r3) <= tolerance:
                    self.log_test("Primary Scenario - R3 Calculation", True, 
                                f"R3 = {r3_calculated:.2f}Ω (expected ~{expected_r3}Ω)")
                    
                    # Verify the calculation pathway matches reference
                    self.verify_calculation_pathway(model, results)
                    return True
                else:
                    self.log_test("Primary Scenario - R3 Calculation", False, 
                                f"R3 calculation incorrect",
                                f"~{expected_r3}Ω", f"{r3_calculated:.2f}Ω")
                    return False
            else:
                self.log_test("Primary Scenario - R3 Calculation", False, 
                            "R3 not calculated", "R3 resistance value", "No result")
                return False
                
        except Exception as e:
            self.log_test("Primary Scenario - R3 Calculation", False, 
                        f"Exception during calculation: {e}")
            return False
    
    def verify_calculation_pathway(self, model, results):
        """Verify that the calculation pathway matches the reference image"""
        print("\n🔍 VERIFYING CALCULATION PATHWAY")
        print("-"*40)
        
        # The reference image shows specific calculation steps
        # 1. H-bridge voltage divider analysis
        # 2. Current calculation through R6
        # 3. Bridge balance equation to find R3
        
        # Check unit conversions
        r6 = model.get_resistor('R6')
        current_base = UnitConverter.to_base_unit(r6.current.value, r6.current.unit, 'current')
        expected_current = -0.000559  # -559μA in Amperes
        
        if abs(current_base - expected_current) < 1e-6:
            self.log_test("Unit Conversion - Current", True, 
                        f"-559μA → {current_base}A")
        else:
            self.log_test("Unit Conversion - Current", False,
                        f"Current conversion incorrect",
                        f"{expected_current}A", f"{current_base}A")
        
        # Verify H-bridge calculation method was used
        if 'R3' in results:
            self.log_test("H-Bridge Method Selection", True,
                        "System correctly selected H-bridge calculation method")
        else:
            self.log_test("H-Bridge Method Selection", False,
                        "System failed to use H-bridge method")
    
    def test_all_resistor_combinations(self):
        """Test scenarios where different resistors are unknown"""
        print("\n🔧 TESTING ALL RESISTOR UNKNOWN COMBINATIONS")
        print("="*60)
        
        # Base scenario values
        base_values = {
            'R1': 180, 'R2': 100, 'R3': 39, 'R4': 22, 'R5': 39, 'R6': 39,
            'supply_voltage': 1.0,
            'r6_voltage': 0.1,
            'r6_current': -559  # μA
        }
        
        # Test each resistor as unknown
        resistors_to_test = ['R1', 'R2', 'R3', 'R4', 'R5', 'R6']
        
        for unknown_resistor in resistors_to_test:
            # Skip R1 and R6 if they can be disabled
            if unknown_resistor in ['R1', 'R6']:
                self.test_resistor_unknown_scenario(base_values, unknown_resistor, enabled=True)
                self.test_resistor_unknown_scenario(base_values, unknown_resistor, enabled=False)
            else:
                # R2, R3, R4, R5 are always enabled (H-bridge components)
                self.test_resistor_unknown_scenario(base_values, unknown_resistor, enabled=True)
    
    def test_resistor_unknown_scenario(self, base_values: Dict, unknown_resistor: str, enabled: bool):
        """Test scenario where one specific resistor is unknown"""
        try:
            model = CircuitModel()
            
            # Set up all known values
            for resistor_name in ['R1', 'R2', 'R3', 'R4', 'R5', 'R6']:
                resistor = model.get_resistor(resistor_name)
                
                if resistor_name == unknown_resistor:
                    # Set this resistor as unknown
                    resistor.resistance.state = ParameterState.UNKNOWN
                    resistor.resistance.value = 0.0
                    resistor.enabled = enabled
                else:
                    # Set known resistance value
                    resistor.resistance.value = base_values[resistor_name]
                    resistor.resistance.unit = 'Ω'
                    resistor.resistance.state = ParameterState.GIVEN
                    
                    # Set enabled state
                    if resistor_name in ['R1', 'R6']:
                        resistor.enabled = True  # For testing, keep enabled
                    else:
                        resistor.enabled = True  # H-bridge components always enabled
            
            # Set R6 voltage and current (measurement data)
            r6 = model.get_resistor('R6')
            r6.voltage.value = base_values['r6_voltage']
            r6.voltage.unit = 'V'
            r6.voltage.state = ParameterState.GIVEN
            
            r6.current.value = base_values['r6_current']
            r6.current.unit = 'μA'
            r6.current.state = ParameterState.GIVEN
            
            # Set supply voltage
            model.supply_voltage.value = base_values['supply_voltage']
            model.supply_voltage.unit = 'V'
            model.supply_voltage.state = ParameterState.GIVEN
            
            # Calculate
            results = self.calculator.solve_circuit(model)
            
            # Check if the unknown resistor was calculated
            if unknown_resistor in results and 'resistance' in results[unknown_resistor]:
                calculated_value = results[unknown_resistor]['resistance']
                expected_value = base_values[unknown_resistor]
                
                # For the unknown resistor, we expect it to be calculated
                # The tolerance depends on the calculation method
                tolerance_percent = 10  # 10% tolerance
                tolerance = expected_value * tolerance_percent / 100
                
                if abs(calculated_value - expected_value) <= tolerance:
                    status_text = "enabled" if enabled else "disabled"
                    self.log_test(f"{unknown_resistor} Unknown ({status_text})", True,
                                f"Calculated {calculated_value:.2f}Ω (expected ~{expected_value}Ω)")
                else:
                    status_text = "enabled" if enabled else "disabled"
                    self.log_test(f"{unknown_resistor} Unknown ({status_text})", False,
                                f"Calculation outside tolerance",
                                f"~{expected_value}Ω ±{tolerance_percent}%", 
                                f"{calculated_value:.2f}Ω")
            else:
                # Check if this is an expected limitation
                if unknown_resistor in ['R1'] and not enabled:
                    # R1 disabled might not be calculable in all scenarios
                    self.log_test(f"{unknown_resistor} Unknown (disabled)", True,
                                "Expected: Cannot calculate disabled R1 in this scenario")
                elif unknown_resistor in ['R6'] and not enabled:
                    # R6 disabled might not be calculable
                    self.log_test(f"{unknown_resistor} Unknown (disabled)", True,
                                "Expected: Cannot calculate disabled R6 in this scenario")
                else:
                    status_text = "enabled" if enabled else "disabled"
                    self.log_test(f"{unknown_resistor} Unknown ({status_text})", False,
                                f"Could not calculate {unknown_resistor}",
                                f"{unknown_resistor} resistance value", "No result")
                    
        except Exception as e:
            status_text = "enabled" if enabled else "disabled"
            self.log_test(f"{unknown_resistor} Unknown ({status_text})", False,
                        f"Exception: {e}")
    
    def test_voltage_variations(self):
        """Test with different supply voltage values"""
        print("\n⚡ TESTING VOLTAGE VARIATIONS")
        print("="*50)
        
        voltage_test_cases = [
            (0.5, "Low voltage"),
            (1.0, "Standard voltage"),
            (5.0, "Medium voltage"),
            (12.0, "High voltage"),
            (24.0, "Very high voltage")
        ]
        
        for supply_v, description in voltage_test_cases:
            try:
                model = CircuitModel()
                
                # Set up standard scenario with R3 unknown
                self.setup_standard_scenario(model, supply_voltage=supply_v)
                
                # Calculate
                results = self.calculator.solve_circuit(model)
                
                if 'R3' in results and 'resistance' in results['R3']:
                    r3_calc = results['R3']['resistance']
                    self.log_test(f"Voltage Test - {description} ({supply_v}V)", True,
                                f"R3 = {r3_calc:.2f}Ω with {supply_v}V supply")
                else:
                    self.log_test(f"Voltage Test - {description} ({supply_v}V)", False,
                                f"Failed to calculate R3 with {supply_v}V supply")
                    
            except Exception as e:
                self.log_test(f"Voltage Test - {description} ({supply_v}V)", False,
                            f"Exception: {e}")
    
    def setup_standard_scenario(self, model: CircuitModel, supply_voltage: float = 1.0):
        """Set up the standard test scenario"""
        # Standard resistor values
        resistor_values = {'R1': 180, 'R2': 100, 'R3': 39, 'R4': 22, 'R5': 39, 'R6': 39}
        
        for name, value in resistor_values.items():
            resistor = model.get_resistor(name)
            if name == 'R3':
                # R3 is unknown
                resistor.resistance.state = ParameterState.UNKNOWN
                resistor.resistance.value = 0.0
            else:
                resistor.resistance.value = value
                resistor.resistance.unit = 'Ω'
                resistor.resistance.state = ParameterState.GIVEN
            resistor.enabled = True
        
        # R6 measurements (scaled proportionally with supply voltage)
        r6 = model.get_resistor('R6')
        r6.voltage.value = 0.1 * (supply_voltage / 1.0)  # Scale with supply
        r6.voltage.unit = 'V'
        r6.voltage.state = ParameterState.GIVEN
        
        r6.current.value = -559  # Keep current constant for now
        r6.current.unit = 'μA'
        r6.current.state = ParameterState.GIVEN
        
        # Supply voltage
        model.supply_voltage.value = supply_voltage
        model.supply_voltage.unit = 'V'
        model.supply_voltage.state = ParameterState.GIVEN

    def test_unit_conversions(self):
        """Test all unit conversion scenarios"""
        print("\n🔄 TESTING UNIT CONVERSIONS")
        print("="*50)

        # Test current unit conversions
        current_test_cases = [
            (-559, 'μA', "Microamperes"),
            (-0.559, 'mA', "Milliamperes"),
            (-0.000559, 'A', "Amperes"),
        ]

        for current_val, current_unit, description in current_test_cases:
            try:
                model = CircuitModel()
                self.setup_standard_scenario(model)

                # Set R6 current in different units
                r6 = model.get_resistor('R6')
                r6.current.value = current_val
                r6.current.unit = current_unit
                r6.current.state = ParameterState.GIVEN

                results = self.calculator.solve_circuit(model)

                if 'R3' in results and 'resistance' in results['R3']:
                    r3_calc = results['R3']['resistance']
                    self.log_test(f"Unit Test - {description}", True,
                                f"R3 = {r3_calc:.2f}Ω with current {current_val}{current_unit}")
                else:
                    self.log_test(f"Unit Test - {description}", False,
                                f"Failed with current {current_val}{current_unit}")

            except Exception as e:
                self.log_test(f"Unit Test - {description}", False, f"Exception: {e}")

        # Test voltage unit conversions
        voltage_test_cases = [
            (100, 'mV', "Millivolts"),
            (0.1, 'V', "Volts"),
        ]

        for voltage_val, voltage_unit, description in voltage_test_cases:
            try:
                model = CircuitModel()
                self.setup_standard_scenario(model)

                # Set R6 voltage in different units
                r6 = model.get_resistor('R6')
                r6.voltage.value = voltage_val
                r6.voltage.unit = voltage_unit
                r6.voltage.state = ParameterState.GIVEN

                results = self.calculator.solve_circuit(model)

                if 'R3' in results and 'resistance' in results['R3']:
                    r3_calc = results['R3']['resistance']
                    self.log_test(f"Unit Test - {description}", True,
                                f"R3 = {r3_calc:.2f}Ω with voltage {voltage_val}{voltage_unit}")
                else:
                    self.log_test(f"Unit Test - {description}", False,
                                f"Failed with voltage {voltage_val}{voltage_unit}")

            except Exception as e:
                self.log_test(f"Unit Test - {description}", False, f"Exception: {e}")

    def test_meter_variations(self):
        """Test different meter types and measurement points"""
        print("\n📏 TESTING METER VARIATIONS")
        print("="*50)

        meter_test_cases = [
            (MeterType.AMMETER, -559, 'μA', "Ammeter measurement"),
            (MeterType.VOLTMETER, 0.1, 'V', "Voltmeter measurement"),
        ]

        for meter_type, measurement_val, measurement_unit, description in meter_test_cases:
            try:
                model = CircuitModel()
                self.setup_standard_scenario(model)

                # Set meter type and measurement
                model.meter_type = meter_type
                model.meter_measurement.value = measurement_val
                model.meter_measurement.unit = measurement_unit
                model.meter_measurement.state = ParameterState.GIVEN

                results = self.calculator.solve_circuit(model)

                if 'R3' in results and 'resistance' in results['R3']:
                    r3_calc = results['R3']['resistance']
                    self.log_test(f"Meter Test - {description}", True,
                                f"R3 = {r3_calc:.2f}Ω with {description}")
                else:
                    self.log_test(f"Meter Test - {description}", False,
                                f"Failed with {description}")

            except Exception as e:
                self.log_test(f"Meter Test - {description}", False, f"Exception: {e}")

    def test_edge_cases(self):
        """Test edge cases with extreme values"""
        print("\n⚠️ TESTING EDGE CASES")
        print("="*50)

        edge_test_cases = [
            ("Very small resistance", {'R2': 0.1, 'R4': 0.1, 'R5': 0.1}),
            ("Very large resistance", {'R2': 1000000, 'R4': 1000000, 'R5': 1000000}),
            ("Mixed extreme values", {'R2': 0.01, 'R4': 1000000, 'R5': 0.01}),
            ("Very small current", {'r6_current': -1, 'current_unit': 'μA'}),
            ("Very large current", {'r6_current': -1000, 'current_unit': 'mA'}),
        ]

        for description, modifications in edge_test_cases:
            try:
                model = CircuitModel()
                self.setup_standard_scenario(model)

                # Apply modifications
                for key, value in modifications.items():
                    if key.startswith('R') and key[1:].isdigit():
                        resistor = model.get_resistor(key)
                        resistor.resistance.value = value
                    elif key == 'r6_current':
                        r6 = model.get_resistor('R6')
                        r6.current.value = value
                    elif key == 'current_unit':
                        r6 = model.get_resistor('R6')
                        r6.current.unit = value

                results = self.calculator.solve_circuit(model)

                if 'R3' in results and 'resistance' in results['R3']:
                    r3_calc = results['R3']['resistance']
                    # For edge cases, just check that we get a reasonable result
                    if 0.001 <= r3_calc <= 1000000:  # Reasonable range
                        self.log_test(f"Edge Case - {description}", True,
                                    f"R3 = {r3_calc:.2f}Ω (reasonable result)")
                    else:
                        self.log_test(f"Edge Case - {description}", False,
                                    f"R3 = {r3_calc:.2f}Ω (unreasonable result)")
                else:
                    self.log_test(f"Edge Case - {description}", False,
                                f"Failed to calculate R3")

            except Exception as e:
                self.log_test(f"Edge Case - {description}", False, f"Exception: {e}")

    def test_invalid_scenarios(self):
        """Test scenarios that should be detected as invalid/impossible"""
        print("\n🚫 TESTING INVALID SCENARIOS")
        print("="*50)

        invalid_test_cases = [
            ("No unknown parameters", "All parameters given"),
            ("Insufficient data", "Too many unknowns"),
            ("Contradictory measurements", "Inconsistent R6 voltage/current"),
        ]

        for description, scenario_type in invalid_test_cases:
            try:
                model = CircuitModel()

                if scenario_type == "All parameters given":
                    # Set all parameters as given (no unknowns)
                    self.setup_standard_scenario(model)
                    r3 = model.get_resistor('R3')
                    r3.resistance.value = 39
                    r3.resistance.unit = 'Ω'
                    r3.resistance.state = ParameterState.GIVEN

                elif scenario_type == "Too many unknowns":
                    # Set multiple resistors as unknown
                    self.setup_standard_scenario(model)
                    for resistor_name in ['R2', 'R3', 'R4']:
                        resistor = model.get_resistor(resistor_name)
                        resistor.resistance.state = ParameterState.UNKNOWN
                        resistor.resistance.value = 0.0

                elif scenario_type == "Inconsistent R6 voltage/current":
                    # Set R6 voltage and current that don't match Ohm's law
                    self.setup_standard_scenario(model)
                    r6 = model.get_resistor('R6')
                    r6.resistance.value = 39
                    r6.resistance.unit = 'Ω'
                    r6.resistance.state = ParameterState.GIVEN
                    r6.voltage.value = 1.0  # 1V
                    r6.current.value = -1   # -1μA (should be much higher for 39Ω)

                results = self.calculator.solve_circuit(model)

                # For invalid scenarios, we expect either no results or an error
                if not results or len(results) == 0:
                    self.log_test(f"Invalid Scenario - {description}", True,
                                f"Correctly detected as invalid: {scenario_type}")
                else:
                    self.log_test(f"Invalid Scenario - {description}", False,
                                f"Should have detected as invalid: {scenario_type}")

            except Exception as e:
                # Exceptions are expected for invalid scenarios
                self.log_test(f"Invalid Scenario - {description}", True,
                            f"Correctly threw exception: {type(e).__name__}")

    def test_disabled_component_scenarios(self):
        """Test scenarios with R1 and R6 disabled"""
        print("\n🔌 TESTING DISABLED COMPONENT SCENARIOS")
        print("="*50)

        disabled_test_cases = [
            ("R1 disabled", 'R1', False),
            ("R6 disabled", 'R6', False),
            ("Both R1 and R6 disabled", 'both', False),
        ]

        for description, component, _ in disabled_test_cases:
            try:
                model = CircuitModel()
                self.setup_standard_scenario(model)

                if component == 'R1':
                    r1 = model.get_resistor('R1')
                    r1.enabled = False
                elif component == 'R6':
                    r6 = model.get_resistor('R6')
                    r6.enabled = False
                elif component == 'both':
                    r1 = model.get_resistor('R1')
                    r6 = model.get_resistor('R6')
                    r1.enabled = False
                    r6.enabled = False

                results = self.calculator.solve_circuit(model)

                if 'R3' in results and 'resistance' in results['R3']:
                    r3_calc = results['R3']['resistance']
                    self.log_test(f"Disabled Test - {description}", True,
                                f"R3 = {r3_calc:.2f}Ω with {description}")
                else:
                    # Some disabled scenarios might not be solvable
                    if component == 'both':
                        self.log_test(f"Disabled Test - {description}", True,
                                    f"Expected: Cannot solve with both R1 and R6 disabled")
                    else:
                        self.log_test(f"Disabled Test - {description}", False,
                                    f"Could not calculate R3 with {description}")

            except Exception as e:
                # Some disabled scenarios might throw exceptions
                if component == 'both':
                    self.log_test(f"Disabled Test - {description}", True,
                                f"Expected exception with both disabled: {type(e).__name__}")
                else:
                    self.log_test(f"Disabled Test - {description}", False,
                                f"Unexpected exception: {e}")
    
    def run_comprehensive_tests(self):
        """Run all comprehensive tests"""
        print("🚀 STARTING COMPREHENSIVE H-BRIDGE TEST SUITE")
        print("="*80)
        print("Testing ALL possible scenarios and parameter combinations")
        print("="*80)
        
        # Test categories
        test_categories = [
            ("Primary Scenario", self.test_primary_scenario),
            ("All Resistor Combinations", self.test_all_resistor_combinations),
            ("Voltage Variations", self.test_voltage_variations),
            ("Unit Conversions", self.test_unit_conversions),
            ("Meter Type Variations", self.test_meter_variations),
            ("Edge Cases", self.test_edge_cases),
            ("Invalid Scenarios", self.test_invalid_scenarios),
            ("Disabled Components", self.test_disabled_component_scenarios),
        ]
        
        category_results = []
        
        for category_name, test_func in test_categories:
            print(f"\n📋 CATEGORY: {category_name}")
            print("-" * 60)
            
            try:
                initial_passed = self.passed_tests
                initial_failed = self.failed_tests
                
                test_func()
                
                category_passed = self.passed_tests - initial_passed
                category_failed = self.failed_tests - initial_failed
                category_total = category_passed + category_failed
                
                if category_total > 0:
                    category_success_rate = (category_passed / category_total) * 100
                    category_results.append((category_name, category_success_rate, category_passed, category_failed))
                    print(f"📊 {category_name}: {category_passed}/{category_total} passed ({category_success_rate:.1f}%)")
                else:
                    category_results.append((category_name, 100.0, 1, 0))
                    print(f"📊 {category_name}: Single test passed")
                    
            except Exception as e:
                print(f"💥 CATEGORY FAILED: {category_name} - {e}")
                category_results.append((category_name, 0.0, 0, 1))
                self.failed_tests += 1
        
        # Print comprehensive summary
        self.print_comprehensive_summary(category_results)
        
        return self.failed_tests == 0
    
    def print_comprehensive_summary(self, category_results):
        """Print detailed test summary"""
        print("\n" + "="*80)
        print("📊 COMPREHENSIVE TEST SUITE SUMMARY")
        print("="*80)
        
        total_tests = self.passed_tests + self.failed_tests
        overall_success_rate = (self.passed_tests / total_tests * 100) if total_tests > 0 else 0
        
        print(f"📈 OVERALL RESULTS:")
        print(f"   Total Tests: {total_tests}")
        print(f"   Passed: {self.passed_tests}")
        print(f"   Failed: {self.failed_tests}")
        print(f"   Success Rate: {overall_success_rate:.1f}%")
        
        print(f"\n📋 CATEGORY BREAKDOWN:")
        for category, success_rate, passed, failed in category_results:
            status = "✅" if success_rate >= 90 else "⚠️" if success_rate >= 70 else "❌"
            print(f"   {status} {category}: {passed}/{passed+failed} ({success_rate:.1f}%)")
        
        if self.failed_tests == 0:
            print("\n🎉 ALL TESTS PASSED! H-Bridge system is fully functional!")
            print("✅ The system can handle ALL possible parameter combinations")
            print("✅ All calculation pathways work correctly")
            print("✅ Unit conversions are accurate")
            print("✅ Edge cases are handled properly")
        else:
            print(f"\n⚠️ {self.failed_tests} tests failed. Issues detected:")
            
            # Show failed tests
            failed_tests = [test for test in self.test_results if not test['passed']]
            for test in failed_tests[:5]:  # Show first 5 failures
                print(f"   ❌ {test['name']}: {test['details']}")
            
            if len(failed_tests) > 5:
                print(f"   ... and {len(failed_tests) - 5} more failures")


def main():
    """Main entry point for comprehensive testing"""
    print("🔧 H-Bridge Circuit Analysis - Comprehensive Test Suite")
    print("="*70)
    print("Testing ALL possible scenarios and parameter combinations")
    print("="*70)
    
    try:
        test_suite = ComprehensiveHBridgeTest()
        success = test_suite.run_comprehensive_tests()
        
        if success:
            print("\n✅ COMPREHENSIVE VERIFICATION COMPLETE - ALL SYSTEMS OPERATIONAL")
            exit(0)
        else:
            print("\n❌ COMPREHENSIVE VERIFICATION FAILED - ISSUES DETECTED")
            exit(1)
            
    except Exception as e:
        print(f"\n💥 TEST SUITE CRASHED: {e}")
        import traceback
        traceback.print_exc()
        exit(2)


if __name__ == "__main__":
    main()
