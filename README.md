# H-Bridge Circuit Analysis Application

A comprehensive Python application for analyzing H-bridge circuits with interactive configuration and mathematical calculations.

## Features

### Circuit Visualization
- **Interactive H-bridge circuit diagram** using schemdraw
- **Real-time circuit updates** when components are modified
- **Visual indication** of enabled/disabled components
- **Professional circuit symbols** and labeling

### Interactive Configuration
- **Right-click configuration** for all resistors (R1-R6)
- **Enable/disable resistors** with visual wire bypass indication
- **Configurable measurement device** (voltmeter/ammeter)
- **Supply voltage configuration**
- **Unit selection** for all electrical parameters

### Analysis Modes
1. **Solve Mode**: Calculate specific unknown values
   - Mark parameters as Given (blue), Unknown (green), or Inactive (white)
   - Input known values and calculate unknowns
   - Comprehensive electrical analysis

2. **List Mode**: Show all possible calculations
   - Display all calculable values with given inputs
   - Comprehensive parameter listing

### Electrical Calculations
- **Ohm's Law calculations** (V = I×R, P = V×I)
- **Wheatstone bridge analysis** for H-bridge configuration
- **Voltage divider calculations**
- **Current distribution analysis**
- **Power calculations** for all components
- **Unit conversions** (Ω/kΩ/MΩ, V/mV/kV, A/mA/μA/nA, W/mW/μW)

### Parameter Management
- **Three-state parameter buttons**:
  - White: Inactive/not used
  - Blue: Given value (user input required)
  - Green: Unknown value (to be calculated)
- **Multiple unit support** with automatic conversion
- **Validation** of circuit configuration before solving

## Installation

1. **Install Python 3.8+**

2. **Install required dependencies**:
   ```bash
   pip install -r requirements.txt
   ```

3. **Run the application**:
   ```bash
   python main.py
   ```

## Usage

### Basic Operation

1. **Start the application** by running `python main.py`
2. **Configure circuit components**:
   - Right-click on resistors to set values or disable them
   - Right-click on the meter to switch between voltage/current measurement
   - Use the Circuit menu for detailed configuration

3. **Set analysis parameters**:
   - Choose Solve or List mode
   - For each resistor, set parameter states (white/blue/green)
   - Input known values for blue parameters

4. **Solve the circuit**:
   - Click "Solve" to perform calculations
   - View results in the right panel
   - Export circuit diagrams if needed

### Example: Analyzing the Reference Circuit

Based on the provided reference image (R5=0Ω example):

1. **Configure resistors**:
   - R1: 180Ω (series resistor)
   - R2: 100Ω (bridge upper-left)
   - R3: 39Ω (bridge lower-left)
   - R4: 22Ω (bridge upper-right)
   - R5: Disabled (R5=0, bypassed)
   - R6: 39Ω (parallel load)

2. **Set supply voltage**: 1V

3. **Configure measurement**: Voltmeter across bridge

4. **Set parameter states**:
   - Mark resistance values as "Given" (blue)
   - Mark desired calculations as "Unknown" (green)

5. **Solve**: Click solve to get all circuit parameters

### Menu Options

- **File Menu**:
  - New Circuit: Reset to default configuration
  - Export Circuit: Save circuit diagram as PNG/SVG
  - Exit: Close application

- **Circuit Menu**:
  - Configure Supply Voltage
  - Configure R1-R6 individually
  - Configure Meter

- **Help Menu**:
  - Instructions: Detailed usage guide
  - About: Application information

## File Structure

```
Hbridge/
├── main.py                 # Main application entry point
├── circuit_model.py        # Circuit state and data model
├── circuit_drawer.py       # Schemdraw visualization
├── electrical_calculator.py # Electrical formulas and calculations
├── gui_components.py       # Custom GUI widgets and dialogs
├── requirements.txt        # Python dependencies
├── test_app.py            # Test script
└── README.md              # This file
```

## Technical Details

### Dependencies
- **tkinter**: GUI framework (built-in with Python)
- **schemdraw**: Professional circuit diagram generation
- **matplotlib**: Backend for schemdraw visualization
- **numpy**: Mathematical calculations

### Electrical Formulas Implemented
- **Ohm's Law**: V = I×R, P = V×I = I²R = V²/R
- **Wheatstone Bridge**: V_bridge = V_supply × (R3/(R1+R3) - R4/(R2+R4))
- **Voltage Divider**: V_out = V_in × R2/(R1+R2)
- **Current Divider**: I1 = I_in × R2/(R1+R2)
- **Parallel Resistance**: 1/R_eq = 1/R1 + 1/R2 + ...
- **Series Resistance**: R_eq = R1 + R2 + ...

### Circuit Analysis Capabilities
- **Complete H-bridge analysis** with all 6 resistors
- **Partial circuit analysis** when components are disabled
- **Bridge balance detection** and unbalanced bridge calculations
- **Load analysis** with parallel resistor R6
- **Series resistance effects** with R1

## Testing

Run the test script to verify functionality:
```bash
python test_app.py
```

This will test:
- Circuit configuration
- Parameter validation
- Basic calculations
- Unit conversions
- Ohm's law calculations
- Wheatstone bridge analysis

## Troubleshooting

### Common Issues

1. **Application won't start**:
   - Ensure all dependencies are installed: `pip install -r requirements.txt`
   - Check Python version (3.8+ required)

2. **Circuit diagram not displaying**:
   - Verify matplotlib and schemdraw installation
   - Check for any error messages in the console

3. **Calculations seem incorrect**:
   - Verify parameter states (blue for given, green for unknown)
   - Check unit selections
   - Ensure sufficient given parameters for solving

4. **Right-click menus not working**:
   - Ensure you're right-clicking directly on circuit elements
   - Try clicking closer to the component symbols

## Future Enhancements

Potential improvements for future versions:
- **AC analysis** capabilities
- **Frequency response** analysis
- **Temperature coefficient** considerations
- **Tolerance analysis** with component variations
- **SPICE integration** for advanced simulation
- **Export to other CAD formats**
- **Component library** with standard values

## License

This application is provided as-is for educational and analysis purposes.

## Support

For issues or questions, please refer to the built-in help system (Help → Instructions) or review the test script for usage examples.
