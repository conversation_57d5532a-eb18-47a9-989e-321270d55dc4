"""
H-Bridge Circuit Analysis Application
Main application window and controller.
"""

import tkinter as tk
from tkinter import ttk, messagebox, simpledialog, filedialog
import sys
import os
import platform

# Add current directory to path for imports
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from circuit_model import CircuitModel, MeterType, ParameterState
from circuit_drawer import CircuitDrawer
from electrical_calculator import ElectricalCalculator
from gui_components import ResistorConfigDialog, MeterConfigDialog, ResultsDisplay


class HBridgeApp:
    """Main application class for H-Bridge analysis"""
    
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("H-Bridge Circuit Analysis - Optimized for HP Spectre 14")

        # Optimize for HP Spectre 14 (high DPI display)
        self.setup_high_dpi()

        # Set optimal window size for HP Spectre 14 (1920x1080 or higher)
        self.root.geometry("1400x900")
        self.root.minsize(1200, 800)

        # Initialize model and calculator
        self.model = CircuitModel()
        self.calculator = ElectricalCalculator()

        # Initialize UI components
        self.circuit_drawer = None
        self.results_display = None

        self.setup_ui()
        self.setup_menu()
        self.setup_right_click_bindings()

    def setup_high_dpi(self):
        """Configure high DPI settings for HP Spectre 14"""
        try:
            # Enable DPI awareness on Windows
            if platform.system() == "Windows":
                from ctypes import windll
                windll.shcore.SetProcessDpiAwareness(1)
        except:
            pass

        # Configure tkinter for high DPI
        self.root.tk.call('tk', 'scaling', 1.5)  # Increase scaling for high DPI

        # Set larger default fonts for better readability
        default_font = ('Segoe UI', 10)
        self.root.option_add('*Font', default_font)

        # Configure ttk styles for better appearance
        style = ttk.Style()
        style.configure('TLabel', font=('Segoe UI', 10))
        style.configure('TButton', font=('Segoe UI', 10), padding=6)
        style.configure('TLabelFrame.Label', font=('Segoe UI', 11, 'bold'))
        style.configure('TNotebook.Tab', font=('Segoe UI', 10), padding=[12, 8])

    def setup_right_click_bindings(self):
        """Setup right-click context menus throughout the application"""
        # Bind right-click to the main window for global context menu
        self.root.bind("<Button-3>", self.show_global_context_menu)

        # The circuit drawer will handle its own right-click events
        # through the on_element_click callback
        
    def setup_ui(self):
        """Setup the main user interface"""
        # Create main paned window
        main_paned = ttk.PanedWindow(self.root, orient=tk.HORIZONTAL)
        main_paned.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # Left panel for circuit diagram
        left_frame = ttk.Frame(main_paned)
        main_paned.add(left_frame, weight=2)
        
        # Circuit diagram frame
        circuit_frame = ttk.LabelFrame(left_frame, text="Circuit Diagram")
        circuit_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # Initialize circuit drawer
        self.circuit_drawer = CircuitDrawer(circuit_frame, self.model)
        self.circuit_drawer.set_click_callback(self.on_element_click)
        
        # Right panel for controls and results
        right_frame = ttk.Frame(main_paned)
        main_paned.add(right_frame, weight=1)
        
        # Mode selection
        mode_frame = ttk.LabelFrame(right_frame, text="Analysis Mode")
        mode_frame.pack(fill=tk.X, padx=5, pady=5)
        
        self.mode_var = tk.StringVar(value="solve")
        ttk.Radiobutton(mode_frame, text="Solve Mode", variable=self.mode_var, 
                       value="solve", command=self.on_mode_change).pack(anchor='w')
        ttk.Radiobutton(mode_frame, text="List Mode", variable=self.mode_var, 
                       value="list", command=self.on_mode_change).pack(anchor='w')
        
        # Control buttons
        control_frame = ttk.Frame(right_frame)
        control_frame.pack(fill=tk.X, padx=5, pady=5)
        
        ttk.Button(control_frame, text="Solve", command=self.solve_circuit).pack(side=tk.LEFT, padx=5)
        ttk.Button(control_frame, text="Reset", command=self.reset_circuit).pack(side=tk.LEFT, padx=5)
        ttk.Button(control_frame, text="Export", command=self.export_circuit).pack(side=tk.LEFT, padx=5)
        
        # Results display
        results_frame = ttk.LabelFrame(right_frame, text="Results")
        results_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        self.results_display = ResultsDisplay(results_frame)
        self.results_display.pack(fill=tk.BOTH, expand=True)
        
        # Status bar
        self.status_var = tk.StringVar(value="Ready")
        status_bar = ttk.Label(self.root, textvariable=self.status_var, relief=tk.SUNKEN)
        status_bar.pack(side=tk.BOTTOM, fill=tk.X)
    
    def setup_menu(self):
        """Setup the application menu"""
        menubar = tk.Menu(self.root)
        self.root.config(menu=menubar)
        
        # File menu
        file_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="File", menu=file_menu)
        file_menu.add_command(label="New Circuit", command=self.new_circuit)
        file_menu.add_separator()
        file_menu.add_command(label="Export Circuit", command=self.export_circuit)
        file_menu.add_separator()
        file_menu.add_command(label="Exit", command=self.root.quit)
        
        # Circuit menu
        circuit_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="Circuit", menu=circuit_menu)
        circuit_menu.add_command(label="Configure Supply Voltage", command=self.configure_supply)
        circuit_menu.add_separator()
        for i in range(1, 7):
            circuit_menu.add_command(label=f"Configure R{i}", 
                                   command=lambda r=i: self.configure_resistor(f"R{r}"))
        circuit_menu.add_separator()
        circuit_menu.add_command(label="Configure Meter", command=self.configure_meter)
        
        # Help menu
        help_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="Help", menu=help_menu)
        help_menu.add_command(label="About", command=self.show_about)
        help_menu.add_command(label="Instructions", command=self.show_instructions)
    
    def show_global_context_menu(self, event):
        """Show global right-click context menu"""
        menu = tk.Menu(self.root, tearoff=0)

        # Quick actions
        menu.add_command(label="Solve Circuit", command=self.solve_circuit)
        menu.add_command(label="Reset Circuit", command=self.reset_circuit)
        menu.add_separator()

        # Configuration shortcuts
        config_menu = tk.Menu(menu, tearoff=0)
        menu.add_cascade(label="Quick Configure", menu=config_menu)
        config_menu.add_command(label="Supply Voltage", command=self.configure_supply)
        config_menu.add_separator()
        for i in range(1, 7):
            config_menu.add_command(label=f"Resistor R{i}",
                                  command=lambda r=i: self.configure_resistor(f"R{r}"))
        config_menu.add_separator()
        config_menu.add_command(label="Meter", command=self.configure_meter)

        # Mode switching
        menu.add_separator()
        mode_menu = tk.Menu(menu, tearoff=0)
        menu.add_cascade(label="Analysis Mode", menu=mode_menu)
        mode_menu.add_command(label="Solve Mode",
                             command=lambda: self.set_mode("solve"))
        mode_menu.add_command(label="List Mode",
                             command=lambda: self.set_mode("list"))

        # Export options
        menu.add_separator()
        menu.add_command(label="Export Circuit", command=self.export_circuit)

        try:
            menu.tk_popup(event.x_root, event.y_root)
        finally:
            menu.grab_release()

    def set_mode(self, mode: str):
        """Set analysis mode programmatically"""
        self.mode_var.set(mode)
        self.on_mode_change()

    def on_element_click(self, element_name: str, x: int, y: int):
        """Handle right-click on circuit elements"""
        if element_name.startswith('R'):
            self.show_resistor_menu(element_name, x, y)
        elif element_name == 'METER':
            self.show_meter_menu(x, y)
    
    def show_resistor_menu(self, resistor_name: str, x: int, y: int):
        """Show context menu for resistor"""
        menu = tk.Menu(self.root, tearoff=0)
        menu.add_command(label=f"Configure {resistor_name}",
                        command=lambda: self.configure_resistor(resistor_name))

        # Only show enable/disable option for R1 and R6
        if self.model.can_disable_resistor(resistor_name):
            resistor = self.model.get_resistor(resistor_name)
            if resistor.enabled:
                menu.add_command(label="Disable",
                               command=lambda: self.toggle_resistor(resistor_name))
            else:
                menu.add_command(label="Enable",
                               command=lambda: self.toggle_resistor(resistor_name))
        else:
            menu.add_separator()
            menu.add_command(label="(Core bridge component - cannot disable)", state='disabled')

        try:
            menu.tk_popup(x, y)
        finally:
            menu.grab_release()
    
    def show_meter_menu(self, x: int, y: int):
        """Show context menu for meter"""
        menu = tk.Menu(self.root, tearoff=0)
        menu.add_command(label="Configure Meter", command=self.configure_meter)
        
        current_type = "Voltmeter" if self.model.meter_type == MeterType.VOLTMETER else "Ammeter"
        new_type = "Switch to Ammeter" if self.model.meter_type == MeterType.VOLTMETER else "Switch to Voltmeter"
        menu.add_command(label=new_type, command=self.toggle_meter_type)
        
        try:
            menu.tk_popup(x, y)
        finally:
            menu.grab_release()
    
    def configure_resistor(self, resistor_name: str):
        """Open resistor configuration dialog"""
        resistor = self.model.get_resistor(resistor_name)
        if resistor:
            dialog = ResistorConfigDialog(self.root, resistor_name, resistor, 
                                        self.on_circuit_change)
    
    def configure_meter(self):
        """Open meter configuration dialog"""
        dialog = MeterConfigDialog(self.root, self.model, self.on_circuit_change)
    
    def configure_supply(self):
        """Configure supply voltage"""
        current_value = self.model.supply_voltage.value
        new_value = simpledialog.askfloat("Supply Voltage",
                                           "Enter supply voltage:",
                                           initialvalue=current_value,
                                           minvalue=0.001)
        if new_value is not None:
            self.model.supply_voltage.value = new_value
            self.on_circuit_change()
    
    def toggle_resistor(self, resistor_name: str):
        """Toggle resistor enabled/disabled state"""
        resistor = self.model.get_resistor(resistor_name)
        if resistor:
            resistor.enabled = not resistor.enabled
            self.on_circuit_change()
    
    def toggle_meter_type(self):
        """Toggle between voltmeter and ammeter"""
        new_type = MeterType.AMMETER if self.model.meter_type == MeterType.VOLTMETER else MeterType.VOLTMETER
        self.model.set_meter_type(new_type)
        self.on_circuit_change()
    
    def on_circuit_change(self):
        """Handle circuit configuration changes"""
        print(f"\n🔄 DEBUG: on_circuit_change called - updating circuit display")

        # Debug: Show current model values
        print(f"🔄 Current model values after change:")
        for name in ['R1', 'R2', 'R3', 'R4', 'R5', 'R6']:
            resistor = self.model.get_resistor(name)
            print(f"🔄   {name}: {resistor.resistance.value}Ω (state: {resistor.resistance.state.name})")

        self.circuit_drawer.update_circuit()
        self.status_var.set("Circuit updated")
    
    def on_mode_change(self):
        """Handle analysis mode changes"""
        self.model.solve_mode = (self.mode_var.get() == "solve")
        self.status_var.set(f"Mode: {self.mode_var.get().title()}")
    
    def solve_circuit(self):
        """Solve the circuit with current configuration"""
        try:
            # CRITICAL: Check if R6 has values and R3 should be calculated
            r6 = self.model.get_resistor('R6')
            r3 = self.model.get_resistor('R3')

            if (r6 and r3 and
                (r6.voltage.value != 0 or r6.current.value != 0) and
                r3.resistance.state != ParameterState.UNKNOWN):

                print("🎯 DETECTED: R6 has values, setting R3 for calculation")
                self.model.set_r3_for_calculation()

            # Validate configuration
            valid, message = self.model.validate_configuration()
            if not valid:
                messagebox.showerror("Configuration Error", message)
                return

            # Debug: Check what components are enabled
            enabled = self.model.get_enabled_resistors()
            print(f"DEBUG: Components for calculation: {list(enabled.keys())}")
            for name, resistor in enabled.items():
                print(f"DEBUG: {name} - enabled={resistor.enabled}, value={resistor.resistance.value}Ω")

            # Perform calculations
            results = self.calculator.solve_circuit(self.model)

            # Debug: Check what's in results
            print(f"DEBUG: Results components: {list(results.keys())}")

            # Display results
            mode = "solve" if self.model.solve_mode else "list"
            self.results_display.display_results(results, mode)

            # Update circuit diagram with calculated results
            self.circuit_drawer.update_with_results(results)

            self.status_var.set("Circuit solved successfully")

        except Exception as e:
            messagebox.showerror("Calculation Error", f"Error solving circuit: {str(e)}")
            self.status_var.set("Error in calculation")
    
    def reset_circuit(self):
        """Reset all circuit parameters"""
        self.model.reset_all_states()
        self.circuit_drawer.update_circuit()
        self.results_display.clear_results()
        self.status_var.set("Circuit reset")
    
    def new_circuit(self):
        """Create a new circuit"""
        self.model = CircuitModel()
        self.circuit_drawer.model = self.model
        self.circuit_drawer.update_circuit()
        self.results_display.clear_results()
        self.status_var.set("New circuit created")
    
    def export_circuit(self):
        """Export circuit diagram"""
        try:
            filename = filedialog.asksaveasfilename(
                defaultextension=".png",
                filetypes=[("PNG files", "*.png"), ("SVG files", "*.svg"), ("All files", "*.*")]
            )
            if filename:
                self.circuit_drawer.export_circuit(filename)
                self.status_var.set(f"Circuit exported to {filename}")
        except Exception as e:
            messagebox.showerror("Export Error", f"Error exporting circuit: {str(e)}")
    
    def show_about(self):
        """Show about dialog"""
        messagebox.showinfo("About", 
                          "H-Bridge Circuit Analysis Application\n\n"
                          "Analyze H-bridge circuits with interactive configuration\n"
                          "and comprehensive electrical calculations.\n\n"
                          "Built with Python, tkinter, and schemdraw.")
    
    def show_instructions(self):
        """Show instructions dialog"""
        instructions = """
H-Bridge Circuit Analysis Instructions:

1. Circuit Configuration:
   - Right-click on resistors to configure values or disable them
   - Right-click on the meter to switch between voltage/current measurement
   - Use the Circuit menu for detailed configuration

2. Analysis Modes:
   - Solve Mode: Calculate specific unknown values
   - List Mode: Show all possible calculations

3. Parameter States:
   - White circle: Parameter not used
   - Blue circle: Known value (input required)
   - Green circle: Unknown value (to be calculated)

4. Solving:
   - Set parameter states and values
   - Click Solve to perform calculations
   - Results will be displayed in the right panel

5. Tips:
   - Ensure you have enough known values to solve for unknowns
   - Use the Reset button to clear all parameter states
   - Export your circuit diagrams for documentation
        """
        
        messagebox.showinfo("Instructions", instructions)
    
    def run(self):
        """Start the application"""
        self.root.mainloop()


def main():
    """Main entry point"""
    try:
        app = HBridgeApp()
        app.run()
    except Exception as e:
        print(f"Error starting application: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
