"""
Test value entry system directly
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from circuit_model import CircuitModel, ParameterState
from electrical_calculator import ElectricalCalculator

def test_direct_value_setting():
    """Test setting values directly on the model"""
    
    print("🧪 TESTING DIRECT VALUE SETTING")
    print("="*50)
    
    # Create model
    model = CircuitModel()
    
    # Get R6
    r6 = model.get_resistor('R6')
    if not r6:
        print("❌ R6 not found!")
        return False
    
    print(f"Initial R6 voltage: {r6.voltage.value} {r6.voltage.unit} (state: {r6.voltage.state.name})")
    print(f"Initial R6 current: {r6.current.value} {r6.current.unit} (state: {r6.current.state.name})")
    
    # Set R6 voltage to GIVEN state and value 0.1
    r6.voltage.state = ParameterState.GIVEN
    r6.voltage.value = 0.1
    r6.voltage.unit = 'V'
    
    print(f"After setting R6 voltage: {r6.voltage.value} {r6.voltage.unit} (state: {r6.voltage.state.name})")
    
    # Set R6 current to GIVEN state and value -559 μA
    r6.current.state = ParameterState.GIVEN
    r6.current.value = -559
    r6.current.unit = 'μA'
    
    print(f"After setting R6 current: {r6.current.value} {r6.current.unit} (state: {r6.current.state.name})")
    
    # Set R3 to UNKNOWN for calculation
    r3 = model.get_resistor('R3')
    r3.resistance.state = ParameterState.UNKNOWN
    r3.resistance.value = 0.0
    
    print(f"R3 set for calculation: {r3.resistance.value} {r3.resistance.unit} (state: {r3.resistance.state.name})")
    
    # Test calculation
    calculator = ElectricalCalculator()
    
    print("\n🧮 TESTING CALCULATION")
    print("-"*30)
    
    try:
        results = calculator.solve_circuit(model)
        
        if 'R3' in results and 'resistance' in results['R3']:
            r3_calc = results['R3']['resistance']
            print(f"✅ SUCCESS! R3 calculated: {r3_calc:.2f}Ω")
            return True
        else:
            print("❌ FAILED: R3 not calculated")
            print("Available results:")
            for comp, params in results.items():
                print(f"  {comp}: {list(params.keys())}")
            return False
            
    except Exception as e:
        print(f"❌ CALCULATION ERROR: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_gui_simulation():
    """Simulate what should happen in the GUI"""
    
    print("\n🖥️ SIMULATING GUI VALUE ENTRY")
    print("="*50)
    
    # This simulates what should happen when user:
    # 1. Right-clicks R6
    # 2. Sets voltage to GIVEN (blue button)
    # 3. Types "0.1" in voltage text box
    # 4. Sets current to GIVEN (blue button)  
    # 5. Types "-559" in current text box
    # 6. Changes current unit to μA
    # 7. Clicks OK
    
    model = CircuitModel()
    r6 = model.get_resistor('R6')
    
    print("Step 1: User clicks voltage state button → GIVEN")
    r6.voltage.state = ParameterState.GIVEN
    print(f"  Voltage state: {r6.voltage.state.name}")
    
    print("Step 2: User types '0.1' in voltage text box")
    # This is what should happen in on_value_change
    r6.voltage.value = 0.1
    r6.voltage.unit = 'V'
    print(f"  Voltage value: {r6.voltage.value} {r6.voltage.unit}")
    
    print("Step 3: User clicks current state button → GIVEN")
    r6.current.state = ParameterState.GIVEN
    print(f"  Current state: {r6.current.state.name}")
    
    print("Step 4: User types '-559' in current text box")
    # This is what should happen in on_value_change
    r6.current.value = -559
    print(f"  Current value: {r6.current.value}")
    
    print("Step 5: User changes current unit to μA")
    # This is what should happen in on_unit_change
    r6.current.unit = 'μA'
    print(f"  Current unit: {r6.current.unit}")
    
    print("Step 6: User clicks OK")
    # Values should be saved to model
    
    print("\nFinal R6 state:")
    print(f"  Voltage: {r6.voltage.value} {r6.voltage.unit} (state: {r6.voltage.state.name})")
    print(f"  Current: {r6.current.value} {r6.current.unit} (state: {r6.current.state.name})")
    
    # Test if this works for calculation
    r3 = model.get_resistor('R3')
    r3.resistance.state = ParameterState.UNKNOWN
    
    calculator = ElectricalCalculator()
    results = calculator.solve_circuit(model)
    
    if 'R3' in results and 'resistance' in results['R3']:
        r3_calc = results['R3']['resistance']
        print(f"\n✅ SIMULATION SUCCESS! R3 = {r3_calc:.2f}Ω")
        return True
    else:
        print(f"\n❌ SIMULATION FAILED: R3 not calculated")
        return False

if __name__ == "__main__":
    print("🧪 VALUE ENTRY TESTING SUITE")
    print("="*60)
    
    test1_passed = test_direct_value_setting()
    test2_passed = test_gui_simulation()
    
    print("\n📊 TEST RESULTS:")
    print(f"Direct value setting: {'✅ PASS' if test1_passed else '❌ FAIL'}")
    print(f"GUI simulation: {'✅ PASS' if test2_passed else '❌ FAIL'}")
    
    if test1_passed and test2_passed:
        print("\n🎉 ALL TESTS PASSED!")
        print("The calculation system works - the issue is in the GUI value entry!")
    else:
        print("\n❌ TESTS FAILED!")
        print("There are issues with the calculation system itself.")
