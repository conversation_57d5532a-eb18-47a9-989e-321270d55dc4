"""
Test unit conversion directly
"""

from electrical_calculator import UnitConverter

def test_unit_conversion():
    """Test the unit conversion system"""
    
    print("🔧 TESTING UNIT CONVERSION")
    print("="*50)
    
    # Test cases
    test_cases = [
        (-559, 'μA', 'current', "User's current value"),
        (0.1, 'V', 'voltage', "User's voltage value"),
        (1000, 'mV', 'voltage', "1000 mV to V"),
        (2.2, 'kΩ', 'resistance', "2.2 kΩ to Ω"),
        (100, 'mW', 'power', "100 mW to W")
    ]
    
    for value, unit, param_type, description in test_cases:
        try:
            base_value = UnitConverter.to_base_unit(value, unit, param_type)
            print(f"✅ {description}: {value} {unit} → {base_value} (base unit)")
            
            # Test reverse conversion
            back_value = UnitConverter.from_base_unit(base_value, unit, param_type)
            print(f"   Reverse: {base_value} → {back_value} {unit}")
            
            if abs(back_value - value) < 1e-10:
                print(f"   ✅ Conversion is correct!")
            else:
                print(f"   ❌ Conversion error: {value} != {back_value}")
                
        except Exception as e:
            print(f"❌ Error converting {value} {unit}: {e}")
        
        print()

if __name__ == "__main__":
    test_unit_conversion()
