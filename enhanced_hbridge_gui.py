"""
Enhanced H-Bridge GUI - Shows ALL possible parameters for calculation
This version displays ALL parameters (R, V, I, P) for ALL components
"""

import tkinter as tk
from tkinter import ttk, messagebox
import sys
import os

sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from circuit_model import CircuitModel, ParameterState, MeterType
from electrical_calculator import ElectricalCalculator


class ParameterRow:
    """A row showing one parameter with state control"""
    
    def __init__(self, parent, component_name, param_name, parameter, callback):
        self.component_name = component_name
        self.param_name = param_name
        self.parameter = parameter
        self.callback = callback
        
        # Create frame for this parameter
        self.frame = tk.Frame(parent, bg='white')
        
        # Component and parameter label
        label_text = f"{component_name} {param_name}"
        self.label = tk.Label(self.frame, text=label_text, width=15, anchor='w',
                             font=('Segoe UI', 10), bg='white')
        self.label.pack(side=tk.LEFT, padx=5)
        
        # State button (colored circle)
        self.state_button = tk.Button(self.frame, text="●", width=3,
                                     command=self.toggle_state,
                                     font=('Arial', 12, 'bold'))
        self.state_button.pack(side=tk.LEFT, padx=5)
        
        # Value entry
        self.value_var = tk.StringVar()
        self.value_entry = tk.Entry(self.frame, textvariable=self.value_var, width=10,
                                   font=('Segoe UI', 10))
        self.value_entry.pack(side=tk.LEFT, padx=5)
        self.value_entry.bind('<KeyRelease>', self.on_value_change)
        
        # Unit selection
        self.unit_var = tk.StringVar()
        units = self.get_units_for_parameter()
        self.unit_combo = ttk.Combobox(self.frame, textvariable=self.unit_var,
                                      values=units, width=8, state='readonly')
        self.unit_combo.pack(side=tk.LEFT, padx=5)
        self.unit_combo.bind('<<ComboboxSelected>>', self.on_unit_change)
        
        # Status label
        self.status_label = tk.Label(self.frame, text="", width=15, anchor='w',
                                    font=('Segoe UI', 9), bg='white')
        self.status_label.pack(side=tk.LEFT, padx=5)
        
        self.update_display()
    
    def get_units_for_parameter(self):
        """Get appropriate units for this parameter type"""
        if self.param_name == 'resistance':
            return ['Ω', 'kΩ', 'MΩ']
        elif self.param_name == 'voltage':
            return ['V', 'mV', 'kV']
        elif self.param_name == 'current':
            return ['A', 'mA', 'μA', 'nA']
        elif self.param_name == 'power':
            return ['W', 'mW', 'μW']
        else:
            return ['']
    
    def toggle_state(self):
        """Cycle through parameter states"""
        if self.parameter.state == ParameterState.INACTIVE:
            self.parameter.state = ParameterState.GIVEN
        elif self.parameter.state == ParameterState.GIVEN:
            self.parameter.state = ParameterState.UNKNOWN
        else:  # UNKNOWN
            self.parameter.state = ParameterState.INACTIVE
        
        self.update_display()
        if self.callback:
            self.callback()
    
    def on_value_change(self, event=None):
        """Handle value changes"""
        if self.parameter.state == ParameterState.GIVEN:
            try:
                value = float(self.value_var.get())
                self.parameter.value = value
                if self.callback:
                    self.callback()
            except ValueError:
                pass  # Invalid input, ignore
    
    def on_unit_change(self, event=None):
        """Handle unit changes"""
        self.parameter.unit = self.unit_var.get()
        if self.callback:
            self.callback()
    
    def update_display(self):
        """Update the visual display based on current state"""
        # Update state button color and text
        if self.parameter.state == ParameterState.INACTIVE:
            self.state_button.config(fg='lightgray', text="⚪")
            self.status_label.config(text="Not used", fg='gray')
            self.value_entry.config(state='disabled')
            self.unit_combo.config(state='disabled')
        elif self.parameter.state == ParameterState.GIVEN:
            self.state_button.config(fg='blue', text="🔵")
            self.status_label.config(text="Enter value", fg='blue')
            self.value_entry.config(state='normal')
            self.unit_combo.config(state='readonly')
        else:  # UNKNOWN
            self.state_button.config(fg='green', text="🟢")
            self.status_label.config(text="Calculate this", fg='green')
            self.value_entry.config(state='disabled')
            self.unit_combo.config(state='disabled')
        
        # Update value display
        if self.parameter.state == ParameterState.GIVEN:
            self.value_var.set(str(self.parameter.value))
        elif self.parameter.state == ParameterState.UNKNOWN:
            if self.parameter.value != 0.0:
                self.value_var.set(f"{self.parameter.value:.6g}")
            else:
                self.value_var.set("?")
        else:
            self.value_var.set("")
        
        # Update unit
        self.unit_var.set(self.parameter.unit)


class EnhancedHBridgeGUI:
    """Enhanced H-Bridge GUI showing ALL parameters for ALL components"""
    
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("Enhanced H-Bridge Analysis - ALL Parameters Visible")
        self.root.geometry("1200x800")
        
        # Initialize model and calculator
        self.model = CircuitModel()
        self.calculator = ElectricalCalculator()
        
        # Parameter rows storage
        self.parameter_rows = []
        
        self.setup_ui()
    
    def setup_ui(self):
        """Setup the enhanced user interface"""
        # Main container
        main_frame = tk.Frame(self.root, bg='white')
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # Title
        title_label = tk.Label(main_frame, text="H-Bridge Circuit Analysis - ALL Parameters",
                              font=('Segoe UI', 16, 'bold'), bg='white')
        title_label.pack(pady=10)
        
        # Instructions
        instructions = """
🔵 BLUE = Given (enter your known values)  🟢 GREEN = Unknown (calculate this)  ⚪ GRAY = Not used
Click the colored circles to change parameter states. Enter values when BLUE. System calculates GREEN parameters.
        """
        inst_label = tk.Label(main_frame, text=instructions, font=('Segoe UI', 10),
                             bg='white', fg='darkblue', justify='center')
        inst_label.pack(pady=5)
        
        # Scrollable parameter list
        canvas = tk.Canvas(main_frame, bg='white')
        scrollbar = ttk.Scrollbar(main_frame, orient="vertical", command=canvas.yview)
        scrollable_frame = tk.Frame(canvas, bg='white')
        
        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )
        
        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)
        
        # Pack canvas and scrollbar
        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")
        
        # Create parameter rows for ALL components and ALL parameters
        self.create_all_parameter_rows(scrollable_frame)
        
        # Control buttons
        button_frame = tk.Frame(main_frame, bg='white')
        button_frame.pack(fill=tk.X, pady=10)
        
        solve_btn = tk.Button(button_frame, text="🧮 SOLVE CIRCUIT", 
                             command=self.solve_circuit,
                             font=('Segoe UI', 12, 'bold'), bg='lightgreen',
                             width=15, height=2)
        solve_btn.pack(side=tk.LEFT, padx=10)
        
        reset_btn = tk.Button(button_frame, text="🔄 RESET ALL", 
                             command=self.reset_all,
                             font=('Segoe UI', 12, 'bold'), bg='lightcoral',
                             width=15, height=2)
        reset_btn.pack(side=tk.LEFT, padx=10)
        
        # Results area
        results_frame = tk.LabelFrame(main_frame, text="📊 CALCULATION RESULTS",
                                     font=('Segoe UI', 12, 'bold'), bg='white')
        results_frame.pack(fill=tk.BOTH, expand=True, pady=10)
        
        self.results_text = tk.Text(results_frame, height=10, font=('Consolas', 10),
                                   bg='#f0f0f0', wrap=tk.WORD)
        results_scroll = ttk.Scrollbar(results_frame, orient="vertical", 
                                      command=self.results_text.yview)
        self.results_text.configure(yscrollcommand=results_scroll.set)
        
        self.results_text.pack(side="left", fill="both", expand=True, padx=5, pady=5)
        results_scroll.pack(side="right", fill="y")
    
    def create_all_parameter_rows(self, parent):
        """Create parameter rows for ALL components and ALL parameters"""
        # Component list
        components = ['R1', 'R2', 'R3', 'R4', 'R5', 'R6']
        parameters = ['resistance', 'voltage', 'current', 'power']
        
        # Add supply voltage
        supply_frame = tk.LabelFrame(parent, text="⚡ SUPPLY VOLTAGE", 
                                   font=('Segoe UI', 11, 'bold'), bg='lightblue')
        supply_frame.pack(fill=tk.X, padx=5, pady=5)
        
        supply_row = ParameterRow(supply_frame, "Supply", "voltage", 
                                 self.model.supply_voltage, self.on_parameter_change)
        supply_row.frame.pack(fill=tk.X, padx=5, pady=2)
        self.parameter_rows.append(supply_row)
        
        # Add all resistor parameters
        for component in components:
            resistor = self.model.get_resistor(component)
            
            # Create frame for this component
            comp_frame = tk.LabelFrame(parent, text=f"🔧 {component}", 
                                     font=('Segoe UI', 11, 'bold'), bg='lightyellow')
            comp_frame.pack(fill=tk.X, padx=5, pady=5)
            
            # Add all parameters for this component
            for param_name in parameters:
                parameter = resistor.get_parameter(param_name)
                row = ParameterRow(comp_frame, component, param_name, 
                                 parameter, self.on_parameter_change)
                row.frame.pack(fill=tk.X, padx=5, pady=2)
                self.parameter_rows.append(row)
        
        # Add meter measurement
        meter_frame = tk.LabelFrame(parent, text="📏 METER MEASUREMENT", 
                                  font=('Segoe UI', 11, 'bold'), bg='lightgreen')
        meter_frame.pack(fill=tk.X, padx=5, pady=5)
        
        # Meter type selection
        meter_type_frame = tk.Frame(meter_frame, bg='lightgreen')
        meter_type_frame.pack(fill=tk.X, padx=5, pady=2)
        
        tk.Label(meter_type_frame, text="Meter Type:", font=('Segoe UI', 10, 'bold'),
                bg='lightgreen').pack(side=tk.LEFT, padx=5)
        
        self.meter_type_var = tk.StringVar(value=self.model.meter_type.value)
        meter_combo = ttk.Combobox(meter_type_frame, textvariable=self.meter_type_var,
                                  values=['voltmeter', 'ammeter'], state='readonly')
        meter_combo.pack(side=tk.LEFT, padx=5)
        meter_combo.bind('<<ComboboxSelected>>', self.on_meter_type_change)
        
        # Meter measurement parameter
        meter_row = ParameterRow(meter_frame, "Meter", "measurement", 
                               self.model.meter_measurement, self.on_parameter_change)
        meter_row.frame.pack(fill=tk.X, padx=5, pady=2)
        self.parameter_rows.append(meter_row)
    
    def on_parameter_change(self):
        """Handle parameter changes"""
        self.update_results_preview()
    
    def on_meter_type_change(self, event=None):
        """Handle meter type changes"""
        meter_type = MeterType.VOLTMETER if self.meter_type_var.get() == 'voltmeter' else MeterType.AMMETER
        self.model.set_meter_type(meter_type)
        
        # Update meter measurement units
        for row in self.parameter_rows:
            if row.component_name == "Meter" and row.param_name == "measurement":
                if meter_type == MeterType.VOLTMETER:
                    row.unit_combo.config(values=['V', 'mV', 'kV'])
                    row.parameter.unit = 'V'
                else:
                    row.unit_combo.config(values=['A', 'mA', 'μA', 'nA'])
                    row.parameter.unit = 'A'
                row.update_display()
                break
    
    def update_results_preview(self):
        """Update the results preview"""
        # Count parameters by state
        given_count = 0
        unknown_count = 0
        
        for row in self.parameter_rows:
            if row.parameter.state == ParameterState.GIVEN:
                given_count += 1
            elif row.parameter.state == ParameterState.UNKNOWN:
                unknown_count += 1
        
        preview_text = f"📊 CURRENT STATUS:\n"
        preview_text += f"🔵 Given parameters: {given_count}\n"
        preview_text += f"🟢 Parameters to calculate: {unknown_count}\n\n"
        
        if unknown_count == 0:
            preview_text += "⚠️ No parameters set for calculation!\n"
            preview_text += "Set some parameters to GREEN (🟢) to calculate them.\n"
        elif given_count < 3:
            preview_text += "⚠️ Need more given values!\n"
            preview_text += "Set more parameters to BLUE (🔵) and enter values.\n"
        else:
            preview_text += "✅ Ready to solve! Click 'SOLVE CIRCUIT' button.\n"
        
        self.results_text.delete(1.0, tk.END)
        self.results_text.insert(tk.END, preview_text)
    
    def solve_circuit(self):
        """Solve the circuit with current configuration"""
        try:
            # Validate configuration
            valid, message = self.model.validate_configuration()
            if not valid:
                messagebox.showerror("Configuration Error", message)
                return
            
            # Perform calculations
            results = self.calculator.solve_circuit(self.model)
            
            # Display results
            self.display_results(results)
            
            # Update parameter rows with calculated values
            self.update_calculated_values(results)
            
        except Exception as e:
            messagebox.showerror("Calculation Error", f"Error solving circuit: {str(e)}")
    
    def display_results(self, results):
        """Display calculation results"""
        self.results_text.delete(1.0, tk.END)
        
        result_text = "🎉 CALCULATION RESULTS:\n"
        result_text += "=" * 50 + "\n\n"
        
        for component_name, params in results.items():
            if component_name == 'METER':
                result_text += f"📏 {component_name}:\n"
            else:
                result_text += f"🔧 {component_name}:\n"
            
            for param_name, value in params.items():
                if param_name == 'resistance':
                    result_text += f"   Resistance: {value:.6g} Ω\n"
                elif param_name == 'voltage':
                    result_text += f"   Voltage: {value:.6g} V\n"
                elif param_name == 'current':
                    result_text += f"   Current: {value:.6g} A\n"
                elif param_name == 'power':
                    result_text += f"   Power: {value:.6g} W\n"
                elif param_name == 'measurement':
                    unit = 'V' if self.model.meter_type == MeterType.VOLTMETER else 'A'
                    result_text += f"   Measurement: {value:.6g} {unit}\n"
            
            result_text += "\n"
        
        result_text += "✅ Calculation completed successfully!\n"
        result_text += "🔄 Updated values are shown in the parameter list above.\n"
        
        self.results_text.insert(tk.END, result_text)
    
    def update_calculated_values(self, results):
        """Update parameter rows with calculated values"""
        for component_name, params in results.items():
            if component_name == 'METER':
                continue
                
            for param_name, value in params.items():
                # Find the corresponding parameter row
                for row in self.parameter_rows:
                    if (row.component_name == component_name and 
                        row.param_name == param_name and
                        row.parameter.state == ParameterState.UNKNOWN):
                        
                        row.parameter.value = value
                        row.update_display()
                        break
    
    def reset_all(self):
        """Reset all parameters"""
        self.model.reset_all_states()
        
        # Update all parameter rows
        for row in self.parameter_rows:
            row.update_display()
        
        self.results_text.delete(1.0, tk.END)
        self.results_text.insert(tk.END, "🔄 All parameters reset to default state.\n")
        self.results_text.insert(tk.END, "Set parameters to BLUE (🔵) to enter values.\n")
        self.results_text.insert(tk.END, "Set parameters to GREEN (🟢) to calculate them.\n")
    
    def run(self):
        """Start the application"""
        self.update_results_preview()
        self.root.mainloop()


def main():
    """Main entry point"""
    try:
        app = EnhancedHBridgeGUI()
        app.run()
    except Exception as e:
        print(f"Error starting application: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
