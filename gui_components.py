"""
GUI Components for H-Bridge Analysis Application
Custom widgets and dialogs for parameter configuration.
"""

import tkinter as tk
from tkinter import ttk, messagebox, simpledialog
from typing import Callable, Dict, List, Optional
from circuit_model import Parameter, ParameterState, ResistorState, MeterType
from electrical_calculator import UnitConverter


class ParameterWidget(tk.Frame):
    """Widget for configuring a single parameter with state buttons"""
    
    def __init__(self, parent, param_name: str, parameter: Parameter, 
                 param_type: str, callback: Optional[Callable] = None):
        super().__init__(parent)
        self.param_name = param_name
        self.parameter = parameter
        self.param_type = param_type
        self.callback = callback
        
        self.setup_ui()
        self.update_display()
    
    def setup_ui(self):
        """Setup the parameter widget UI"""
        # Parameter name label
        tk.Label(self, text=self.param_name.capitalize(), width=10).grid(row=0, column=0, padx=5)
        
        # State button (circle)
        self.state_button = tk.Button(self, text="●", width=3, height=1,
                                    command=self.toggle_state)
        self.state_button.grid(row=0, column=1, padx=2)
        
        # Value entry
        self.value_var = tk.StringVar()
        self.value_entry = tk.Entry(self, textvariable=self.value_var, width=10)
        self.value_entry.grid(row=0, column=2, padx=2)
        self.value_entry.bind('<FocusOut>', self.on_value_change)
        self.value_entry.bind('<Return>', self.on_value_change)
        
        # Unit dropdown
        self.unit_var = tk.StringVar()
        self.unit_combo = ttk.Combobox(self, textvariable=self.unit_var, width=8)
        self.unit_combo.grid(row=0, column=3, padx=2)
        self.unit_combo.bind('<<ComboboxSelected>>', self.on_unit_change)
        
        # Set unit options based on parameter type
        self.setup_units()
    
    def setup_units(self):
        """Setup unit options based on parameter type"""
        unit_options = {
            'resistance': ['Ω', 'kΩ', 'MΩ'],
            'voltage': ['V', 'mV', 'kV'],
            'current': ['A', 'mA', 'μA', 'nA'],
            'power': ['W', 'mW', 'μW']
        }
        
        options = unit_options.get(self.param_type, [''])
        self.unit_combo['values'] = options
        if options and not self.parameter.unit:
            self.parameter.unit = options[0]
        self.unit_var.set(self.parameter.unit)
    
    def toggle_state(self):
        """Toggle parameter state: inactive -> given -> unknown -> inactive"""
        current_state = self.parameter.state
        
        if current_state == ParameterState.INACTIVE:
            self.parameter.state = ParameterState.GIVEN
        elif current_state == ParameterState.GIVEN:
            self.parameter.state = ParameterState.UNKNOWN
        else:  # UNKNOWN
            self.parameter.state = ParameterState.INACTIVE
        
        self.update_display()
        if self.callback:
            self.callback()
    
    def update_display(self):
        """Update widget appearance based on parameter state"""
        state = self.parameter.state
        
        if state == ParameterState.INACTIVE:
            self.state_button.config(bg='white', fg='black')
            self.value_entry.config(state='disabled')
        elif state == ParameterState.GIVEN:
            self.state_button.config(bg='blue', fg='white')
            self.value_entry.config(state='normal')
        else:  # UNKNOWN
            self.state_button.config(bg='green', fg='white')
            self.value_entry.config(state='disabled')
        
        # Update value display
        if state != ParameterState.INACTIVE:
            self.value_var.set(str(self.parameter.value))
        else:
            self.value_var.set('')
    
    def on_value_change(self, event=None):
        """Handle value entry changes"""
        if self.parameter.state == ParameterState.GIVEN:
            try:
                value = float(self.value_var.get())
                self.parameter.value = value
                if self.callback:
                    self.callback()
            except ValueError:
                messagebox.showerror("Invalid Value", "Please enter a valid number")
                self.value_var.set(str(self.parameter.value))
    
    def on_unit_change(self, event=None):
        """Handle unit selection changes"""
        self.parameter.unit = self.unit_var.get()
        if self.callback:
            self.callback()


class ResistorConfigDialog(tk.Toplevel):
    """Dialog for configuring resistor parameters"""
    
    def __init__(self, parent, resistor_name: str, resistor: ResistorState, 
                 callback: Optional[Callable] = None):
        super().__init__(parent)
        self.resistor_name = resistor_name
        self.resistor = resistor
        self.callback = callback
        
        self.title(f"Configure {resistor_name}")
        self.geometry("400x300")
        self.transient(parent)
        self.grab_set()
        
        self.setup_ui()
    
    def setup_ui(self):
        """Setup the resistor configuration dialog"""
        main_frame = tk.Frame(self)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # Enabled/Disabled toggle (only for R1 and R6)
        if self.resistor_name in ['R1', 'R6']:
            self.enabled_var = tk.BooleanVar(value=self.resistor.enabled)
            enabled_check = tk.Checkbutton(main_frame, text=f"Enable {self.resistor_name}",
                                         variable=self.enabled_var,
                                         command=self.on_enabled_change)
            enabled_check.pack(anchor='w', pady=5)
        else:
            # Core bridge components cannot be disabled
            tk.Label(main_frame, text=f"{self.resistor_name} (Core H-bridge component - always enabled)",
                    font=('Arial', 9, 'italic')).pack(anchor='w', pady=5)
            self.enabled_var = tk.BooleanVar(value=True)  # Always enabled
        
        # Parameters frame
        params_frame = tk.LabelFrame(main_frame, text="Parameters")
        params_frame.pack(fill=tk.BOTH, expand=True, pady=10)
        
        # Create parameter widgets
        self.param_widgets = {}
        param_types = {
            'resistance': 'resistance',
            'voltage': 'voltage', 
            'current': 'current',
            'power': 'power'
        }
        
        for i, (param_name, param_type) in enumerate(param_types.items()):
            param = self.resistor.get_parameter(param_name)
            widget = ParameterWidget(params_frame, param_name, param, param_type)
            widget.grid(row=i, column=0, sticky='ew', pady=2)
            self.param_widgets[param_name] = widget
        
        # Buttons
        button_frame = tk.Frame(main_frame)
        button_frame.pack(fill=tk.X, pady=10)
        
        tk.Button(button_frame, text="OK", command=self.on_ok).pack(side=tk.RIGHT, padx=5)
        tk.Button(button_frame, text="Cancel", command=self.on_cancel).pack(side=tk.RIGHT)
        
        if not self.resistor.enabled:
            self.disable_parameters()
    
    def on_enabled_change(self):
        """Handle enabled/disabled state change"""
        # Only allow changes for R1 and R6
        if self.resistor_name in ['R1', 'R6']:
            enabled = self.enabled_var.get()
            if enabled:
                self.enable_parameters()
            else:
                self.disable_parameters()
        else:
            # Core bridge components are always enabled
            self.enabled_var.set(True)
    
    def enable_parameters(self):
        """Enable parameter configuration"""
        for widget in self.param_widgets.values():
            # Enable individual components within the parameter widget
            for child in widget.winfo_children():
                if hasattr(child, 'config'):
                    try:
                        child.config(state='normal')
                    except tk.TclError:
                        pass  # Some widgets don't support state

    def disable_parameters(self):
        """Disable parameter configuration"""
        for widget in self.param_widgets.values():
            # Disable individual components within the parameter widget
            for child in widget.winfo_children():
                if hasattr(child, 'config'):
                    try:
                        child.config(state='disabled')
                    except tk.TclError:
                        pass  # Some widgets don't support state
    
    def on_ok(self):
        """Apply changes and close dialog"""
        # Only update enabled state for R1 and R6
        if self.resistor_name in ['R1', 'R6']:
            self.resistor.enabled = self.enabled_var.get()
        else:
            # Core bridge components are always enabled
            self.resistor.enabled = True

        if self.callback:
            self.callback()

        self.destroy()
    
    def on_cancel(self):
        """Cancel changes and close dialog"""
        self.destroy()


class MeterConfigDialog(tk.Toplevel):
    """Dialog for configuring measurement device"""
    
    def __init__(self, parent, model, callback: Optional[Callable] = None):
        super().__init__(parent)
        self.model = model
        self.callback = callback
        
        self.title("Configure Measurement Device")
        self.geometry("300x200")
        self.transient(parent)
        self.grab_set()
        
        self.setup_ui()
    
    def setup_ui(self):
        """Setup the meter configuration dialog"""
        main_frame = tk.Frame(self)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # Meter type selection
        type_frame = tk.LabelFrame(main_frame, text="Measurement Type")
        type_frame.pack(fill=tk.X, pady=10)
        
        self.meter_type_var = tk.StringVar(value=self.model.meter_type.value)
        
        tk.Radiobutton(type_frame, text="Voltmeter", 
                      variable=self.meter_type_var, 
                      value=MeterType.VOLTMETER.value,
                      command=self.on_type_change).pack(anchor='w')
        
        tk.Radiobutton(type_frame, text="Ammeter", 
                      variable=self.meter_type_var, 
                      value=MeterType.AMMETER.value,
                      command=self.on_type_change).pack(anchor='w')
        
        # Measurement parameter
        param_frame = tk.LabelFrame(main_frame, text="Measurement Value")
        param_frame.pack(fill=tk.X, pady=10)
        
        self.param_widget = ParameterWidget(param_frame, "measurement", 
                                          self.model.meter_measurement,
                                          "voltage" if self.model.meter_type == MeterType.VOLTMETER else "current")
        self.param_widget.pack(fill=tk.X, padx=5, pady=5)
        
        # Buttons
        button_frame = tk.Frame(main_frame)
        button_frame.pack(fill=tk.X, pady=10)
        
        tk.Button(button_frame, text="OK", command=self.on_ok).pack(side=tk.RIGHT, padx=5)
        tk.Button(button_frame, text="Cancel", command=self.on_cancel).pack(side=tk.RIGHT)
    
    def on_type_change(self):
        """Handle meter type change"""
        meter_type = MeterType(self.meter_type_var.get())
        self.model.set_meter_type(meter_type)
        
        # Update parameter widget
        param_type = "voltage" if meter_type == MeterType.VOLTMETER else "current"
        self.param_widget.param_type = param_type
        self.param_widget.setup_units()
    
    def on_ok(self):
        """Apply changes and close dialog"""
        meter_type = MeterType(self.meter_type_var.get())
        self.model.set_meter_type(meter_type)
        
        if self.callback:
            self.callback()
        
        self.destroy()
    
    def on_cancel(self):
        """Cancel changes and close dialog"""
        self.destroy()


class ResultsDisplay(tk.Frame):
    """Widget for displaying calculation results"""
    
    def __init__(self, parent):
        super().__init__(parent)
        self.setup_ui()
    
    def setup_ui(self):
        """Setup the results display"""
        # Title
        tk.Label(self, text="Calculation Results", font=('Arial', 12, 'bold')).pack(pady=5)
        
        # Results text area with scrollbar
        text_frame = tk.Frame(self)
        text_frame.pack(fill=tk.BOTH, expand=True)
        
        self.results_text = tk.Text(text_frame, height=10, wrap=tk.WORD)
        scrollbar = tk.Scrollbar(text_frame, orient=tk.VERTICAL, command=self.results_text.yview)
        self.results_text.configure(yscrollcommand=scrollbar.set)
        
        self.results_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
    
    def display_results(self, results: Dict[str, Dict[str, float]], mode: str = "solve"):
        """Display calculation results"""
        self.results_text.delete(1.0, tk.END)
        
        if not results:
            self.results_text.insert(tk.END, "No results to display.\n")
            return
        
        if mode == "solve":
            self.results_text.insert(tk.END, "Calculated Values:\n\n")
        else:
            self.results_text.insert(tk.END, "All Possible Calculations:\n\n")
        
        for component, params in results.items():
            self.results_text.insert(tk.END, f"{component}:\n")
            for param_name, value in params.items():
                if isinstance(value, float):
                    self.results_text.insert(tk.END, f"  {param_name}: {value:.6f}\n")
                else:
                    self.results_text.insert(tk.END, f"  {param_name}: {value}\n")
            self.results_text.insert(tk.END, "\n")
    
    def clear_results(self):
        """Clear the results display"""
        self.results_text.delete(1.0, tk.END)
