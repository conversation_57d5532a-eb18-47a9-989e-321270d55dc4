"""
GUI Components for H-Bridge Analysis Application
Custom widgets and dialogs for parameter configuration.
Optimized for HP Spectre 14 high-DPI display with immediate right-click access.
"""

import tkinter as tk
from tkinter import ttk, messagebox, simpledialog
from typing import Callable, Dict, List, Optional
import platform
from circuit_model import Parameter, ParameterState, ResistorState, MeterType, CircuitModel
from electrical_calculator import UnitConverter


def setup_high_dpi_dialog(dialog):
    """Configure dialog for high DPI displays like HP Spectre 14"""
    try:
        # Enable DPI awareness on Windows
        if platform.system() == "Windows":
            dialog.tk.call('tk', 'scaling', 1.4)  # Scale for high DPI
    except:
        pass

    # Configure fonts for better readability
    default_font = ('Segoe UI', 11)
    dialog.option_add('*Font', default_font)


class ParameterWidget(tk.Frame):
    """Widget for configuring a single parameter with state buttons"""
    
    def __init__(self, parent, param_name: str, parameter: Parameter, 
                 param_type: str, callback: Optional[Callable] = None):
        super().__init__(parent)
        self.param_name = param_name
        self.parameter = parameter
        self.param_type = param_type
        self.callback = callback
        
        self.setup_ui()
        self.update_display()
    
    def setup_ui(self):
        """Setup the parameter widget UI with clear value entry instructions"""
        # Parameter name label
        param_label = tk.Label(self, text=self.param_name.capitalize(), width=12,
                              font=('Segoe UI', 10, 'bold'))
        param_label.grid(row=0, column=0, padx=5, sticky='w')

        # State button (circle) with tooltip
        self.state_button = tk.Button(self, text="●", width=4, height=1,
                                    command=self.toggle_state,
                                    font=('Arial', 12, 'bold'))
        self.state_button.grid(row=0, column=1, padx=2)

        # Value entry with placeholder
        self.value_var = tk.StringVar()
        self.value_entry = tk.Entry(self, textvariable=self.value_var, width=12,
                                   font=('Segoe UI', 10))
        self.value_entry.grid(row=0, column=2, padx=2)
        self.value_entry.bind('<FocusOut>', self.on_value_change)
        self.value_entry.bind('<Return>', self.on_value_change)

        # Unit dropdown
        self.unit_var = tk.StringVar()
        self.unit_combo = ttk.Combobox(self, textvariable=self.unit_var, width=8,
                                      font=('Segoe UI', 10))
        self.unit_combo.grid(row=0, column=3, padx=2)
        self.unit_combo.bind('<<ComboboxSelected>>', self.on_unit_change)

        # Help label
        self.help_label = tk.Label(self, text="← Click ● to enable",
                                  font=('Segoe UI', 8, 'italic'), fg='gray')
        self.help_label.grid(row=0, column=4, padx=5, sticky='w')

        # Set unit options based on parameter type
        self.setup_units()
    
    def setup_units(self):
        """Setup unit options based on parameter type"""
        unit_options = {
            'resistance': ['Ω', 'kΩ', 'MΩ'],
            'voltage': ['V', 'mV', 'kV'],
            'current': ['A', 'mA', 'μA', 'nA'],
            'power': ['W', 'mW', 'μW']
        }
        
        options = unit_options.get(self.param_type, [''])
        self.unit_combo['values'] = options
        if options and not self.parameter.unit:
            self.parameter.unit = options[0]
        self.unit_var.set(self.parameter.unit)
    
    def toggle_state(self):
        """Toggle parameter state: inactive -> given -> unknown -> inactive"""
        current_state = self.parameter.state
        
        if current_state == ParameterState.INACTIVE:
            self.parameter.state = ParameterState.GIVEN
        elif current_state == ParameterState.GIVEN:
            self.parameter.state = ParameterState.UNKNOWN
        else:  # UNKNOWN
            self.parameter.state = ParameterState.INACTIVE
        
        self.update_display()
        if self.callback:
            self.callback()
    
    def update_display(self):
        """Update widget appearance based on parameter state"""
        state = self.parameter.state

        if state == ParameterState.INACTIVE:
            self.state_button.config(bg='lightgray', fg='black', text='●')
            self.value_entry.config(state='disabled', bg='#f0f0f0')
            self.help_label.config(text="← Click ● to enable", fg='gray')
            self.value_var.set('')
        elif state == ParameterState.GIVEN:
            self.state_button.config(bg='blue', fg='white', text='●')
            self.value_entry.config(state='normal', bg='white')
            self.help_label.config(text="← ENTER VALUE HERE", fg='blue', font=('Segoe UI', 8, 'bold'))
            # Set focus to entry when enabled
            self.value_entry.focus_set()
        else:  # UNKNOWN
            self.state_button.config(bg='green', fg='white', text='●')
            self.value_entry.config(state='disabled', bg='#e8f5e8')
            self.help_label.config(text="← Will be calculated", fg='green')

        # Update value display
        if state != ParameterState.INACTIVE:
            self.value_var.set(str(self.parameter.value))
        else:
            self.value_var.set('')
    
    def on_value_change(self, event=None):
        """Handle value entry changes with proper unit conversion"""
        if self.parameter.state == ParameterState.GIVEN:
            try:
                value = float(self.value_var.get())
                # Store the value as entered (in the selected unit)
                self.parameter.value = value
                # Ensure unit is set
                if not self.parameter.unit:
                    self.parameter.unit = self.unit_var.get()
                if self.callback:
                    self.callback()
            except ValueError:
                messagebox.showerror("Invalid Value", "Please enter a valid number")
                self.value_var.set(str(self.parameter.value))

    def on_unit_change(self, event=None):
        """Handle unit selection changes with value conversion"""
        old_unit = self.parameter.unit
        new_unit = self.unit_var.get()

        # Convert value if both units are valid and different
        if (old_unit and new_unit and old_unit != new_unit and
            self.parameter.state == ParameterState.GIVEN and self.parameter.value != 0):
            try:
                from electrical_calculator import UnitConverter
                # Convert from old unit to base unit, then to new unit
                base_value = UnitConverter.to_base_unit(self.parameter.value, old_unit, self.param_type)
                new_value = UnitConverter.from_base_unit(base_value, new_unit, self.param_type)
                self.parameter.value = new_value
                self.value_var.set(f"{new_value:.6g}")  # Update display with converted value
            except:
                pass  # If conversion fails, just update the unit

        self.parameter.unit = new_unit
        if self.callback:
            self.callback()


class ResistorConfigDialog(tk.Toplevel):
    """Enhanced dialog for configuring resistor parameters - optimized for HP Spectre 14"""

    def __init__(self, parent, resistor_name: str, resistor: ResistorState,
                 callback: Optional[Callable] = None):
        super().__init__(parent)
        self.resistor_name = resistor_name
        self.resistor = resistor
        self.callback = callback

        # Configure for HP Spectre 14 high-DPI display
        setup_high_dpi_dialog(self)

        self.title(f"Configure {resistor_name} - H-Bridge Circuit Analysis")
        self.geometry("700x600")  # Standardized size for HP Spectre 14
        self.transient(parent)
        self.grab_set()

        # Center the dialog on screen
        self.center_on_screen()

        self.setup_ui()

        # Focus and bring to front immediately
        self.focus_force()
        self.lift()

    def center_on_screen(self):
        """Center dialog on screen for optimal viewing on HP Spectre 14"""
        self.update_idletasks()
        width = self.winfo_width()
        height = self.winfo_height()
        x = (self.winfo_screenwidth() // 2) - (width // 2)
        y = (self.winfo_screenheight() // 2) - (height // 2)
        self.geometry(f"{width}x{height}+{x}+{y}")
    
    def setup_ui(self):
        """Setup the enhanced resistor configuration dialog for HP Spectre 14"""
        main_frame = tk.Frame(self, bg='white')
        main_frame.pack(fill=tk.BOTH, expand=True, padx=15, pady=15)

        # Title section with component info
        title_frame = tk.Frame(main_frame, bg='white')
        title_frame.pack(fill=tk.X, pady=(0, 15))

        tk.Label(title_frame, text=f"Configure {self.resistor_name}",
                font=('Segoe UI', 16, 'bold'), bg='white').pack(anchor='w')

        # Current values display
        current_frame = tk.LabelFrame(main_frame, text="Current Values",
                                    font=('Segoe UI', 12, 'bold'), bg='white')
        current_frame.pack(fill=tk.X, pady=(0, 15))

        self.display_current_values(current_frame)

        # Enabled/Disabled toggle (only for R1 and R6)
        if self.resistor_name in ['R1', 'R6']:
            enable_frame = tk.LabelFrame(main_frame, text="Component State",
                                       font=('Segoe UI', 12, 'bold'), bg='white')
            enable_frame.pack(fill=tk.X, pady=(0, 15))

            self.enabled_var = tk.BooleanVar(value=self.resistor.enabled)
            enabled_check = tk.Checkbutton(enable_frame, text=f"Enable {self.resistor_name}",
                                         variable=self.enabled_var,
                                         command=self.on_enabled_change,
                                         font=('Segoe UI', 11), bg='white')
            enabled_check.pack(anchor='w', padx=10, pady=8)

            # Add explanation
            if self.resistor_name == 'R1':
                explanation = "When disabled: R1 becomes a short circuit (wire)"
            else:  # R6
                explanation = "When disabled: R6 disappears completely from circuit"
            tk.Label(enable_frame, text=explanation, font=('Segoe UI', 9, 'italic'),
                    fg='gray', bg='white').pack(anchor='w', padx=10)
        else:
            # Core bridge components cannot be disabled
            enable_frame = tk.LabelFrame(main_frame, text="Component State",
                                       font=('Segoe UI', 12, 'bold'), bg='white')
            enable_frame.pack(fill=tk.X, pady=(0, 15))

            tk.Label(enable_frame, text=f"{self.resistor_name} (Core H-bridge component - always enabled)",
                    font=('Segoe UI', 11, 'italic'), fg='blue', bg='white').pack(anchor='w', padx=10, pady=8)
            self.enabled_var = tk.BooleanVar(value=True)  # Always enabled
        
        # Parameters frame with enhanced layout
        params_frame = tk.LabelFrame(main_frame, text="Parameter Configuration",
                                   font=('Segoe UI', 12, 'bold'), bg='white')
        params_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 15))

        # Add parameter state legend and instructions
        legend_frame = tk.Frame(params_frame, bg='white')
        legend_frame.pack(fill=tk.X, padx=10, pady=5)

        tk.Label(legend_frame, text="HOW TO ENTER VALUES:", font=('Segoe UI', 11, 'bold'),
                fg='red', bg='white').pack(anchor='w')

        instruction_text = "1. Click the colored circle (●) to set parameter state\n2. When BLUE = you can enter values in the text box\n3. Select units from dropdown"
        tk.Label(legend_frame, text=instruction_text, font=('Segoe UI', 10),
                fg='darkblue', bg='white', justify='left').pack(anchor='w', pady=2)

        tk.Label(legend_frame, text="Parameter States:", font=('Segoe UI', 10, 'bold'),
                bg='white').pack(anchor='w', pady=(10, 0))

        legend_info = tk.Frame(legend_frame, bg='white')
        legend_info.pack(fill=tk.X, pady=2)

        # State indicators with clear explanations
        tk.Label(legend_info, text="●", fg='lightgray', bg='white', font=('Arial', 14, 'bold')).pack(side=tk.LEFT)
        tk.Label(legend_info, text="Inactive (not used)", font=('Segoe UI', 9), bg='white').pack(side=tk.LEFT, padx=(2, 15))
        tk.Label(legend_info, text="●", fg='blue', bg='white', font=('Arial', 14, 'bold')).pack(side=tk.LEFT)
        tk.Label(legend_info, text="Given (ENTER VALUES HERE)", font=('Segoe UI', 9, 'bold'), fg='blue', bg='white').pack(side=tk.LEFT, padx=(2, 15))
        tk.Label(legend_info, text="●", fg='green', bg='white', font=('Arial', 14, 'bold')).pack(side=tk.LEFT)
        tk.Label(legend_info, text="Unknown (calculate this)", font=('Segoe UI', 9), bg='white').pack(side=tk.LEFT, padx=(2, 10))

        # Create enhanced parameter widgets
        self.param_widgets = {}
        param_types = {
            'resistance': 'resistance',
            'voltage': 'voltage',
            'current': 'current',
            'power': 'power'
        }

        params_container = tk.Frame(params_frame, bg='white')
        params_container.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        for i, (param_name, param_type) in enumerate(param_types.items()):
            param = self.resistor.get_parameter(param_name)
            widget = ParameterWidget(params_container, param_name, param, param_type, self.on_parameter_change)
            widget.grid(row=i, column=0, sticky='ew', pady=5, padx=5)
            widget.configure(bg='white')
            self.param_widgets[param_name] = widget

        # Configure grid weights
        params_container.grid_columnconfigure(0, weight=1)

        # Buttons with enhanced styling
        button_frame = tk.Frame(main_frame, bg='white')
        button_frame.pack(fill=tk.X, pady=15)

        # Apply, OK, Cancel buttons
        apply_btn = tk.Button(button_frame, text="Apply", command=self.on_apply,
                             font=('Segoe UI', 11), bg='lightblue', width=8)
        apply_btn.pack(side=tk.RIGHT, padx=5)

        ok_btn = tk.Button(button_frame, text="OK", command=self.on_ok,
                          font=('Segoe UI', 11), bg='lightgreen', width=8)
        ok_btn.pack(side=tk.RIGHT, padx=5)

        cancel_btn = tk.Button(button_frame, text="Cancel", command=self.on_cancel,
                              font=('Segoe UI', 11), width=8)
        cancel_btn.pack(side=tk.RIGHT)

        if not self.resistor.enabled:
            self.disable_parameters()

    def display_current_values(self, parent):
        """Display current calculated values"""
        values_text = tk.Text(parent, height=4, width=50, font=('Consolas', 10),
                             bg='#f0f0f0', state='disabled')
        values_text.pack(fill=tk.X, padx=10, pady=5)

        # Display current values (this would be populated with actual calculated values)
        values_text.config(state='normal')
        values_text.insert(tk.END, f"Resistance: {self.resistor.resistance.value} {self.resistor.resistance.unit}\n")
        values_text.insert(tk.END, f"Voltage: {self.resistor.voltage.value} {self.resistor.voltage.unit}\n")
        values_text.insert(tk.END, f"Current: {self.resistor.current.value} {self.resistor.current.unit}\n")
        values_text.insert(tk.END, f"Power: {self.resistor.power.value} {self.resistor.power.unit}")
        values_text.config(state='disabled')

    def on_parameter_change(self):
        """Handle parameter changes with real-time updates"""
        if self.callback:
            self.callback()

    def on_apply(self):
        """Apply changes without closing dialog"""
        # Only update enabled state for R1 and R6
        if self.resistor_name in ['R1', 'R6']:
            self.resistor.enabled = self.enabled_var.get()
        else:
            # Core bridge components are always enabled
            self.resistor.enabled = True

        if self.callback:
            self.callback()

        # Update current values display
        for widget in self.winfo_children():
            if isinstance(widget, tk.Frame):
                for child in widget.winfo_children():
                    if isinstance(child, tk.LabelFrame) and "Current Values" in child.cget('text'):
                        for subchild in child.winfo_children():
                            if isinstance(subchild, tk.Text):
                                subchild.config(state='normal')
                                subchild.delete(1.0, tk.END)
                                subchild.insert(tk.END, f"Resistance: {self.resistor.resistance.value} {self.resistor.resistance.unit}\n")
                                subchild.insert(tk.END, f"Voltage: {self.resistor.voltage.value} {self.resistor.voltage.unit}\n")
                                subchild.insert(tk.END, f"Current: {self.resistor.current.value} {self.resistor.current.unit}\n")
                                subchild.insert(tk.END, f"Power: {self.resistor.power.value} {self.resistor.power.unit}")
                                subchild.config(state='disabled')
    
    def on_enabled_change(self):
        """Handle enabled/disabled state change"""
        # Only allow changes for R1 and R6
        if self.resistor_name in ['R1', 'R6']:
            enabled = self.enabled_var.get()
            if enabled:
                self.enable_parameters()
            else:
                self.disable_parameters()
        else:
            # Core bridge components are always enabled
            self.enabled_var.set(True)
    
    def enable_parameters(self):
        """Enable parameter configuration"""
        for widget in self.param_widgets.values():
            # Enable individual components within the parameter widget
            for child in widget.winfo_children():
                if hasattr(child, 'config'):
                    try:
                        child.config(state='normal')
                    except tk.TclError:
                        pass  # Some widgets don't support state

    def disable_parameters(self):
        """Disable parameter configuration"""
        for widget in self.param_widgets.values():
            # Disable individual components within the parameter widget
            for child in widget.winfo_children():
                if hasattr(child, 'config'):
                    try:
                        child.config(state='disabled')
                    except tk.TclError:
                        pass  # Some widgets don't support state
    
    def on_ok(self):
        """Apply changes and close dialog"""
        # Only update enabled state for R1 and R6
        if self.resistor_name in ['R1', 'R6']:
            self.resistor.enabled = self.enabled_var.get()
        else:
            # Core bridge components are always enabled
            self.resistor.enabled = True

        if self.callback:
            self.callback()

        self.destroy()
    
    def on_cancel(self):
        """Cancel changes and close dialog"""
        self.destroy()


class VoltageSourceConfigDialog(tk.Toplevel):
    """Enhanced dialog for configuring voltage source - optimized for HP Spectre 14"""

    def __init__(self, parent, model, callback: Optional[Callable] = None):
        super().__init__(parent)
        self.model = model
        self.callback = callback

        # Configure for HP Spectre 14 high-DPI display
        setup_high_dpi_dialog(self)

        self.title("Configure Voltage Source - H-Bridge Circuit Analysis")
        self.geometry("700x600")  # Standardized size for HP Spectre 14
        self.transient(parent)
        self.grab_set()

        # Center the dialog on screen
        self.center_on_screen()

        self.setup_ui()

        # Focus and bring to front immediately
        self.focus_force()
        self.lift()

    def center_on_screen(self):
        """Center dialog on screen for optimal viewing on HP Spectre 14"""
        self.update_idletasks()
        width = self.winfo_width()
        height = self.winfo_height()
        x = (self.winfo_screenwidth() // 2) - (width // 2)
        y = (self.winfo_screenheight() // 2) - (height // 2)
        self.geometry(f"{width}x{height}+{x}+{y}")

    def setup_ui(self):
        """Setup the voltage source configuration dialog"""
        main_frame = tk.Frame(self, bg='white')
        main_frame.pack(fill=tk.BOTH, expand=True, padx=15, pady=15)

        # Title section
        title_frame = tk.Frame(main_frame, bg='white')
        title_frame.pack(fill=tk.X, pady=(0, 15))

        tk.Label(title_frame, text="Configure Voltage Source",
                font=('Segoe UI', 16, 'bold'), bg='white').pack(anchor='w')

        # Current value display
        current_frame = tk.LabelFrame(main_frame, text="Current Value",
                                    font=('Segoe UI', 12, 'bold'), bg='white')
        current_frame.pack(fill=tk.X, pady=(0, 15))

        current_text = tk.Text(current_frame, height=2, width=40, font=('Consolas', 11),
                              bg='#f0f0f0', state='disabled')
        current_text.pack(fill=tk.X, padx=10, pady=5)

        current_text.config(state='normal')
        current_text.insert(tk.END, f"Supply Voltage: {self.model.supply_voltage.value} {self.model.supply_voltage.unit}")
        current_text.config(state='disabled')

        # Voltage configuration
        config_frame = tk.LabelFrame(main_frame, text="Voltage Configuration",
                                   font=('Segoe UI', 12, 'bold'), bg='white')
        config_frame.pack(fill=tk.X, pady=(0, 15))

        # Voltage parameter widget
        self.voltage_widget = ParameterWidget(config_frame, "supply voltage",
                                            self.model.supply_voltage, "voltage",
                                            self.on_voltage_change)
        self.voltage_widget.pack(fill=tk.X, padx=10, pady=10)
        self.voltage_widget.configure(bg='white')

        # Buttons with enhanced styling
        button_frame = tk.Frame(main_frame, bg='white')
        button_frame.pack(fill=tk.X, pady=15)

        apply_btn = tk.Button(button_frame, text="Apply", command=self.on_apply,
                             font=('Segoe UI', 11), bg='lightblue', width=8)
        apply_btn.pack(side=tk.RIGHT, padx=5)

        ok_btn = tk.Button(button_frame, text="OK", command=self.on_ok,
                          font=('Segoe UI', 11), bg='lightgreen', width=8)
        ok_btn.pack(side=tk.RIGHT, padx=5)

        cancel_btn = tk.Button(button_frame, text="Cancel", command=self.on_cancel,
                              font=('Segoe UI', 11), width=8)
        cancel_btn.pack(side=tk.RIGHT)

    def on_voltage_change(self):
        """Handle voltage changes with real-time updates"""
        if self.callback:
            self.callback()

    def on_apply(self):
        """Apply changes without closing dialog"""
        if self.callback:
            self.callback()

    def on_ok(self):
        """Apply changes and close dialog"""
        if self.callback:
            self.callback()
        self.destroy()

    def on_cancel(self):
        """Cancel changes and close dialog"""
        self.destroy()


class MeterConfigDialog(tk.Toplevel):
    """Enhanced dialog for configuring measurement device - optimized for HP Spectre 14"""

    def __init__(self, parent, model, callback: Optional[Callable] = None):
        super().__init__(parent)
        self.model = model
        self.callback = callback

        # Configure for HP Spectre 14 high-DPI display
        setup_high_dpi_dialog(self)

        self.title("Configure Measurement Device - H-Bridge Circuit Analysis")
        self.geometry("700x600")  # Standardized size for HP Spectre 14
        self.transient(parent)
        self.grab_set()

        # Center the dialog on screen
        self.center_on_screen()

        self.setup_ui()

        # Focus and bring to front immediately
        self.focus_force()
        self.lift()

    def center_on_screen(self):
        """Center dialog on screen for optimal viewing on HP Spectre 14"""
        self.update_idletasks()
        width = self.winfo_width()
        height = self.winfo_height()
        x = (self.winfo_screenwidth() // 2) - (width // 2)
        y = (self.winfo_screenheight() // 2) - (height // 2)
        self.geometry(f"{width}x{height}+{x}+{y}")
    
    def setup_ui(self):
        """Setup the enhanced meter configuration dialog for HP Spectre 14"""
        main_frame = tk.Frame(self, bg='white')
        main_frame.pack(fill=tk.BOTH, expand=True, padx=15, pady=15)

        # Title section
        title_frame = tk.Frame(main_frame, bg='white')
        title_frame.pack(fill=tk.X, pady=(0, 15))

        tk.Label(title_frame, text="Configure Measurement Device",
                font=('Segoe UI', 16, 'bold'), bg='white').pack(anchor='w')

        # Current measurement display
        current_frame = tk.LabelFrame(main_frame, text="Current Configuration",
                                    font=('Segoe UI', 12, 'bold'), bg='white')
        current_frame.pack(fill=tk.X, pady=(0, 15))

        current_text = tk.Text(current_frame, height=3, width=40, font=('Consolas', 11),
                              bg='#f0f0f0', state='disabled')
        current_text.pack(fill=tk.X, padx=10, pady=5)

        current_text.config(state='normal')
        meter_type_str = "Voltmeter" if self.model.meter_type == MeterType.VOLTMETER else "Ammeter"
        current_text.insert(tk.END, f"Type: {meter_type_str}\n")
        current_text.insert(tk.END, f"Measurement: {self.model.meter_measurement.value} {self.model.meter_measurement.unit}\n")
        current_text.insert(tk.END, f"Position: Across bridge diagonal")
        current_text.config(state='disabled')

        # Meter type selection with enhanced styling
        type_frame = tk.LabelFrame(main_frame, text="Measurement Type",
                                 font=('Segoe UI', 12, 'bold'), bg='white')
        type_frame.pack(fill=tk.X, pady=(0, 15))

        self.meter_type_var = tk.StringVar(value=self.model.meter_type.value)

        # Radio buttons with descriptions
        volt_frame = tk.Frame(type_frame, bg='white')
        volt_frame.pack(fill=tk.X, padx=10, pady=5)

        tk.Radiobutton(volt_frame, text="Voltmeter",
                      variable=self.meter_type_var,
                      value=MeterType.VOLTMETER.value,
                      command=self.on_type_change,
                      font=('Segoe UI', 11), bg='white').pack(anchor='w')
        tk.Label(volt_frame, text="Measures voltage difference across bridge diagonal",
                font=('Segoe UI', 9, 'italic'), fg='gray', bg='white').pack(anchor='w', padx=20)

        amp_frame = tk.Frame(type_frame, bg='white')
        amp_frame.pack(fill=tk.X, padx=10, pady=5)

        tk.Radiobutton(amp_frame, text="Ammeter",
                      variable=self.meter_type_var,
                      value=MeterType.AMMETER.value,
                      command=self.on_type_change,
                      font=('Segoe UI', 11), bg='white').pack(anchor='w')
        tk.Label(amp_frame, text="Measures current flow through bridge diagonal",
                font=('Segoe UI', 9, 'italic'), fg='gray', bg='white').pack(anchor='w', padx=20)

        # Measurement parameter configuration
        param_frame = tk.LabelFrame(main_frame, text="Measurement Configuration",
                                  font=('Segoe UI', 12, 'bold'), bg='white')
        param_frame.pack(fill=tk.X, pady=(0, 15))

        self.param_widget = ParameterWidget(param_frame, "measurement",
                                          self.model.meter_measurement,
                                          "voltage" if self.model.meter_type == MeterType.VOLTMETER else "current",
                                          self.on_measurement_change)
        self.param_widget.pack(fill=tk.X, padx=10, pady=10)
        self.param_widget.configure(bg='white')

        # Measurement range info
        range_info = tk.Label(param_frame, text="Set parameter state to configure measurement expectations",
                            font=('Segoe UI', 9, 'italic'), fg='gray', bg='white')
        range_info.pack(anchor='w', padx=10, pady=(0, 10))

        # Buttons with enhanced styling
        button_frame = tk.Frame(main_frame, bg='white')
        button_frame.pack(fill=tk.X, pady=15)

        apply_btn = tk.Button(button_frame, text="Apply", command=self.on_apply,
                             font=('Segoe UI', 11), bg='lightblue', width=8)
        apply_btn.pack(side=tk.RIGHT, padx=5)

        ok_btn = tk.Button(button_frame, text="OK", command=self.on_ok,
                          font=('Segoe UI', 11), bg='lightgreen', width=8)
        ok_btn.pack(side=tk.RIGHT, padx=5)

        cancel_btn = tk.Button(button_frame, text="Cancel", command=self.on_cancel,
                              font=('Segoe UI', 11), width=8)
        cancel_btn.pack(side=tk.RIGHT)

    def on_measurement_change(self):
        """Handle measurement parameter changes"""
        if self.callback:
            self.callback()

    def on_apply(self):
        """Apply changes without closing dialog"""
        meter_type = MeterType(self.meter_type_var.get())
        self.model.set_meter_type(meter_type)

        if self.callback:
            self.callback()
    
    def on_type_change(self):
        """Handle meter type change"""
        meter_type = MeterType(self.meter_type_var.get())
        self.model.set_meter_type(meter_type)
        
        # Update parameter widget
        param_type = "voltage" if meter_type == MeterType.VOLTMETER else "current"
        self.param_widget.param_type = param_type
        self.param_widget.setup_units()
    
    def on_ok(self):
        """Apply changes and close dialog"""
        meter_type = MeterType(self.meter_type_var.get())
        self.model.set_meter_type(meter_type)
        
        if self.callback:
            self.callback()
        
        self.destroy()
    
    def on_cancel(self):
        """Cancel changes and close dialog"""
        self.destroy()


class ResultsDisplay(tk.Frame):
    """Widget for displaying calculation results"""
    
    def __init__(self, parent):
        super().__init__(parent)
        self.setup_ui()
    
    def setup_ui(self):
        """Setup the results display"""
        # Title
        tk.Label(self, text="Calculation Results", font=('Arial', 12, 'bold')).pack(pady=5)
        
        # Results text area with scrollbar
        text_frame = tk.Frame(self)
        text_frame.pack(fill=tk.BOTH, expand=True)
        
        self.results_text = tk.Text(text_frame, height=10, wrap=tk.WORD)
        scrollbar = tk.Scrollbar(text_frame, orient=tk.VERTICAL, command=self.results_text.yview)
        self.results_text.configure(yscrollcommand=scrollbar.set)
        
        self.results_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
    
    def display_results(self, results: Dict[str, Dict[str, float]], mode: str = "solve", model=None):
        """Display calculation results with proper units"""
        self.results_text.delete(1.0, tk.END)

        if not results:
            self.results_text.insert(tk.END, "No results to display.\n")
            return

        if mode == "solve":
            self.results_text.insert(tk.END, "✅ CALCULATED VALUES WITH UNITS:\n")
            self.results_text.insert(tk.END, "=" * 50 + "\n\n")
        else:
            self.results_text.insert(tk.END, "📊 ALL POSSIBLE CALCULATIONS:\n")
            self.results_text.insert(tk.END, "=" * 50 + "\n\n")

        # Unit mapping for proper display
        unit_map = {
            'resistance': 'Ω',
            'voltage': 'V',
            'current': 'A',
            'power': 'W'
        }

        for component, params in results.items():
            self.results_text.insert(tk.END, f"🔧 {component}:\n")

            for param_name, value in params.items():
                if isinstance(value, float):
                    # Get appropriate unit
                    unit = unit_map.get(param_name, '')

                    # Format value with appropriate precision
                    if param_name == 'resistance':
                        if value >= 1e6:
                            formatted_value = f"{value/1e6:.3f}"
                            unit = "MΩ"
                        elif value >= 1e3:
                            formatted_value = f"{value/1e3:.3f}"
                            unit = "kΩ"
                        else:
                            formatted_value = f"{value:.3f}"
                    elif param_name == 'current':
                        if abs(value) < 1e-6:
                            formatted_value = f"{value*1e9:.3f}"
                            unit = "nA"
                        elif abs(value) < 1e-3:
                            formatted_value = f"{value*1e6:.3f}"
                            unit = "μA"
                        elif abs(value) < 1:
                            formatted_value = f"{value*1e3:.3f}"
                            unit = "mA"
                        else:
                            formatted_value = f"{value:.6f}"
                    elif param_name == 'voltage':
                        if abs(value) < 1e-3:
                            formatted_value = f"{value*1e6:.3f}"
                            unit = "μV"
                        elif abs(value) < 1:
                            formatted_value = f"{value*1e3:.3f}"
                            unit = "mV"
                        else:
                            formatted_value = f"{value:.6f}"
                    elif param_name == 'power':
                        if abs(value) < 1e-6:
                            formatted_value = f"{value*1e9:.3f}"
                            unit = "nW"
                        elif abs(value) < 1e-3:
                            formatted_value = f"{value*1e6:.3f}"
                            unit = "μW"
                        elif abs(value) < 1:
                            formatted_value = f"{value*1e3:.3f}"
                            unit = "mW"
                        else:
                            formatted_value = f"{value:.6f}"
                    else:
                        formatted_value = f"{value:.6f}"

                    self.results_text.insert(tk.END, f"   {param_name.capitalize()}: {formatted_value} {unit}\n")
                else:
                    self.results_text.insert(tk.END, f"   {param_name.capitalize()}: {value}\n")
            self.results_text.insert(tk.END, "\n")

        # Add calculation summary
        if mode == "solve":
            self.results_text.insert(tk.END, "💡 CALCULATION SUMMARY:\n")
            self.results_text.insert(tk.END, f"   • Total components analyzed: {len(results)}\n")
            total_params = sum(len(params) for params in results.values())
            self.results_text.insert(tk.END, f"   • Total parameters calculated: {total_params}\n")
            self.results_text.insert(tk.END, "   • All values include proper units\n")
            self.results_text.insert(tk.END, "   • Multiple calculation paths used\n")
    
    def clear_results(self):
        """Clear the results display"""
        self.results_text.delete(1.0, tk.END)
