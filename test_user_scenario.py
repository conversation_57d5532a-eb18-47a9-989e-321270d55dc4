"""
Test the exact user scenario:
- R6 current = -559 μA
- R6 voltage = 0.1 V
- Calculate R3
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from circuit_model import CircuitModel, ParameterState, MeterType
from electrical_calculator import ElectricalCalculator

def test_user_scenario():
    """Test the user's exact scenario"""
    
    print("🎯 TESTING USER'S EXACT SCENARIO")
    print("="*60)
    print("User wants:")
    print("- R6 current = -559 μA")
    print("- R6 voltage = 0.1 V")
    print("- Calculate R3 resistance")
    print("="*60)
    
    # Create fresh model and calculator
    model = CircuitModel()
    calculator = ElectricalCalculator()
    
    print("\n📝 STEP 1: Setting up user's values")
    print("-"*40)
    
    # Get R6 resistor
    r6 = model.get_resistor('R6')
    if not r6:
        print("❌ ERROR: R6 not found!")
        return
    
    # Set R6 current to -559 μA
    r6.current.value = -559
    r6.current.unit = 'μA'
    r6.current.state = ParameterState.GIVEN
    print(f"✅ Set R6 current: {r6.current.value} {r6.current.unit} (state: {r6.current.state.name})")
    
    # Set R6 voltage to 0.1 V
    r6.voltage.value = 0.1
    r6.voltage.unit = 'V'
    r6.voltage.state = ParameterState.GIVEN
    print(f"✅ Set R6 voltage: {r6.voltage.value} {r6.voltage.unit} (state: {r6.voltage.state.name})")
    
    # Set R3 to UNKNOWN for calculation
    r3 = model.get_resistor('R3')
    if r3:
        r3.resistance.state = ParameterState.UNKNOWN
        r3.resistance.value = 0.0  # Clear default value
        print(f"✅ Set R3 for calculation: state = {r3.resistance.state.name}, value = {r3.resistance.value}")
    else:
        print("❌ ERROR: R3 not found!")
        return
    
    # Set meter to ammeter with the current value
    model.meter_type = MeterType.AMMETER
    model.meter_measurement.value = -559  # This should be -559, not -559000000
    model.meter_measurement.state = ParameterState.GIVEN
    print(f"✅ Set meter: {model.meter_measurement.value} (type: {model.meter_type.name})")
    
    print("\n📝 STEP 2: Verify setup")
    print("-"*40)
    
    # Verify what we set
    print("Verification:")
    print(f"R6 current: {r6.current.value} {r6.current.unit} (state: {r6.current.state.name})")
    print(f"R6 voltage: {r6.voltage.value} {r6.voltage.unit} (state: {r6.voltage.state.name})")
    print(f"R3 resistance: {r3.resistance.value} {r3.resistance.unit} (state: {r3.resistance.state.name})")
    print(f"Meter: {model.meter_measurement.value} (state: {model.meter_measurement.state.name})")
    
    # Test unit conversion
    print("\n📝 STEP 3: Test unit conversion")
    print("-"*40)
    
    current_base = calculator.converter.to_base_unit(r6.current.value, r6.current.unit, 'current')
    voltage_base = calculator.converter.to_base_unit(r6.voltage.value, r6.voltage.unit, 'voltage')
    
    print(f"R6 current conversion: {r6.current.value} {r6.current.unit} → {current_base} A")
    print(f"R6 voltage conversion: {r6.voltage.value} {r6.voltage.unit} → {voltage_base} V")
    
    print("\n📝 STEP 4: Calculate circuit")
    print("-"*40)
    
    try:
        results = calculator.solve_circuit(model)
        
        print("\n📊 RESULTS:")
        print("="*40)
        
        if 'R3' in results and 'resistance' in results['R3']:
            r3_calculated = results['R3']['resistance']
            print(f"🎉 SUCCESS! R3 = {r3_calculated:.2f} Ω")
        else:
            print("❌ FAILED: R3 not calculated")
            print("Available results:")
            for component, params in results.items():
                print(f"  {component}: {list(params.keys())}")
        
        # Show all results
        print("\nAll calculated values:")
        for component, params in results.items():
            print(f"\n{component}:")
            for param, value in params.items():
                print(f"  {param}: {value}")
                
    except Exception as e:
        print(f"❌ CALCULATION FAILED: {e}")
        import traceback
        traceback.print_exc()

def test_manual_calculation():
    """Manual calculation to verify expected result"""
    
    print("\n🧮 MANUAL CALCULATION FOR VERIFICATION")
    print("="*60)
    
    # Given values
    r6_current = -559e-6  # -559 μA in A
    r6_voltage = 0.1      # 0.1 V
    v_supply = 1.0        # 1.0 V (default)
    
    # Default resistor values
    r2 = 100.0  # Ω
    r4 = 22.0   # Ω
    r5 = 39.0   # Ω
    
    print(f"Given:")
    print(f"  R6 current = {r6_current*1e6:.0f} μA = {r6_current} A")
    print(f"  R6 voltage = {r6_voltage} V")
    print(f"  Supply voltage = {v_supply} V")
    print(f"  R2 = {r2} Ω, R4 = {r4} Ω, R5 = {r5} Ω")
    
    # H-bridge equation: V_R6 = V_supply * (R4/(R4+R5) - R2/(R2+R3))
    # Solving for R3:
    # 0.1 = 1.0 * (22/(22+39) - 100/(100+R3))
    # 0.1 = 1.0 * (22/61 - 100/(100+R3))
    # 0.1 = 0.3607 - 100/(100+R3)
    # 100/(100+R3) = 0.3607 - 0.1 = 0.2607
    # 100 = 0.2607 * (100+R3)
    # 100 = 26.07 + 0.2607*R3
    # 73.93 = 0.2607*R3
    # R3 = 73.93/0.2607 = 283.7Ω
    
    right_side = r4 / (r4 + r5)
    voltage_ratio = r6_voltage / v_supply
    denominator = right_side - voltage_ratio
    
    print(f"\nCalculation:")
    print(f"  Right side (R4/(R4+R5)) = {r4}/{r4+r5} = {right_side:.4f}")
    print(f"  Voltage ratio (V_R6/V_supply) = {r6_voltage}/{v_supply} = {voltage_ratio:.4f}")
    print(f"  Denominator = {right_side:.4f} - {voltage_ratio:.4f} = {denominator:.4f}")
    
    if abs(denominator) > 1e-6:
        r3_calculated = r2 * (1 / denominator - 1)
        print(f"  R3 = R2 * (1/denominator - 1)")
        print(f"  R3 = {r2} * (1/{denominator:.4f} - 1)")
        print(f"  R3 = {r2} * ({1/denominator:.4f} - 1)")
        print(f"  R3 = {r2} * {1/denominator - 1:.4f}")
        print(f"  R3 = {r3_calculated:.2f} Ω")
        
        print(f"\n🎯 EXPECTED RESULT: R3 = {r3_calculated:.2f} Ω")
    else:
        print(f"❌ Cannot calculate - denominator too small: {denominator}")

if __name__ == "__main__":
    test_user_scenario()
    test_manual_calculation()
