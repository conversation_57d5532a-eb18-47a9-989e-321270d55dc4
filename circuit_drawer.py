"""
Circuit Drawer for H-Bridge Analysis Application
Uses schemdraw to create interactive circuit diagrams.
"""

import schemdraw
import schemdraw.elements as elm
import matplotlib.pyplot as plt
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
import tkinter as tk
from typing import Dict, Tuple, Optional, Callable
from circuit_model import CircuitModel, MeterType


class CircuitDrawer:
    """<PERSON><PERSON> drawing and interaction with the H-bridge circuit"""
    
    def __init__(self, parent_frame: tk.Frame, model: CircuitModel):
        self.parent_frame = parent_frame
        self.model = model
        self.canvas = None
        self.figure = None
        self.drawing = None
        
        # Store element positions for click detection
        self.element_positions = {}
        self.click_callback = None
        self.calculated_results = {}  # Store calculated results for display
        
        # Initialize the drawing
        self.setup_canvas()
        self.draw_circuit()
    
    def setup_canvas(self):
        """Setup matplotlib canvas optimized for HP Spectre 14"""
        # Optimize figure size and DPI for HP Spectre 14 high-resolution display
        self.figure = plt.Figure(figsize=(14, 10), dpi=120)
        self.canvas = FigureCanvasTkAgg(self.figure, self.parent_frame)
        self.canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True)

        # Configure for high DPI display
        self.figure.patch.set_facecolor('white')

        # Bind click events (both left and right click)
        self.canvas.mpl_connect('button_press_event', self._on_click)
    
    def set_click_callback(self, callback: Callable[[str, int, int], None]):
        """Set callback for element clicks (element_name, x, y)"""
        self.click_callback = callback
    
    def draw_circuit(self):
        """Draw perfectly clean H-bridge circuit using coordinate-based approach"""
        self.figure.clear()

        # Create schemdraw drawing with clean professional appearance
        self.drawing = schemdraw.Drawing(unit=4, lw=2)

        # Define precise coordinate points for clean layout
        # Input voltage source and R1
        p_U_top = (0, 4)
        p_U_bot = (0, 0)

        p_R1_mid_top = (4, 4)
        p_R1_mid_bot = (4, 0)

        # H-Bridge points
        p_bridge_R2_top = (8, 4)
        p_bridge_R3_bot = (8, 0)
        p_bridge_mid_left = (8, 2)

        p_bridge_R4_top = (12, 4)
        p_bridge_R5_bot = (12, 0)
        p_bridge_mid_right = (12, 2)

        # Output R6 and terminals
        p_R6_top = (16, 4)
        p_R6_bot = (16, 0)

        p_out_top = (18, 4)
        p_out_bot = (18, 0)

        # Draw Components and Connections with perfect alignment

        # Voltage Source
        vs = self.drawing.add(elm.SourceV().at(p_U_top).down().to(p_U_bot).label(f"U\\n{self.model.supply_voltage.value}V", loc='bot'))
        self._store_element_position('VOLTAGE_SOURCE', vs)

        # R1 - Series resistor (or short circuit if disabled)
        r1_enabled = self.model.get_resistor('R1').enabled
        if r1_enabled:
            r1_value = self.model.get_resistor('R1').resistance.value
            r1 = self.drawing.add(elm.Resistor().at(p_U_top).right().to(p_R1_mid_top).label(f"R₁\\n{int(r1_value)}"))
            self._store_element_position('R1', r1)
        else:
            # R1 disabled = short circuit (direct wire connection)
            r1 = self.drawing.add(elm.Line().at(p_U_top).right().to(p_R1_mid_top))
            self._store_element_position('R1', r1)

        # Bottom rail from voltage source to R1
        self.drawing.add(elm.Line().at(p_U_bot).right().to(p_R1_mid_bot))

        # Top rail from R1 to bridge top-left
        self.drawing.add(elm.Line().at(p_R1_mid_top).to(p_bridge_R2_top))
        # Bottom rail from R1 to bridge bottom-left
        self.drawing.add(elm.Line().at(p_R1_mid_bot).to(p_bridge_R3_bot))

        # H-Bridge Resistors (R2, R3, R4, R5) - show ACTUAL entered values
        r2_value = self.model.get_resistor('R2').resistance.value
        r2_label = f"R₂\\n{r2_value:.0f}Ω" if r2_value != int(r2_value) else f"R₂\\n{int(r2_value)}Ω"
        r2 = self.drawing.add(elm.Resistor().at(p_bridge_R2_top).down().to(p_bridge_mid_left).label(r2_label))
        self._store_element_position('R2', r2)

        r3_value = self.model.get_resistor('R3').resistance.value
        r3_label = f"R₃\\n{r3_value:.0f}Ω" if r3_value != int(r3_value) else f"R₃\\n{int(r3_value)}Ω"
        r3 = self.drawing.add(elm.Resistor().at(p_bridge_mid_left).down().to(p_bridge_R3_bot).label(r3_label))
        self._store_element_position('R3', r3)

        r4_value = self.model.get_resistor('R4').resistance.value
        r4_label = f"R₄\\n{r4_value:.0f}Ω" if r4_value != int(r4_value) else f"R₄\\n{int(r4_value)}Ω"
        r4 = self.drawing.add(elm.Resistor().at(p_bridge_R4_top).down().to(p_bridge_mid_right).label(r4_label))
        self._store_element_position('R4', r4)

        # R5 - ALWAYS show as resistor with actual entered value (including 0Ω)
        r5_value = self.model.get_resistor('R5').resistance.value
        r5_label = f"R₅\\n{r5_value:.0f}Ω" if r5_value != int(r5_value) else f"R₅\\n{int(r5_value)}Ω"
        r5 = self.drawing.add(elm.Resistor().at(p_bridge_mid_right).down().to(p_bridge_R5_bot).label(r5_label))
        self._store_element_position('R5', r5)

        # Connect top and bottom bridge rails horizontally - perfectly straight
        self.drawing.add(elm.Line().at(p_bridge_R2_top).to(p_bridge_R4_top))  # Top horizontal connection
        self.drawing.add(elm.Line().at(p_bridge_R3_bot).to(p_bridge_R5_bot))  # Bottom horizontal connection

        # R6 - Load resistor (disappears when disabled)
        r6_enabled = self.model.get_resistor('R6').enabled
        if r6_enabled:
            r6_value = self.model.get_resistor('R6').resistance.value
            self.drawing.add(elm.Line().at(p_bridge_R4_top).right().to(p_R6_top))
            self.drawing.add(elm.Line().at(p_bridge_R5_bot).right().to(p_R6_bot))
            r6 = self.drawing.add(elm.Resistor().at(p_R6_top).down().to(p_R6_bot).label(f"R₆\\n{int(r6_value)}"))
            self._store_element_position('R6', r6)

            # Output terminals with clean dots (only when R6 is enabled)
            self.drawing.add(elm.Line().at(p_R6_top).right().to(p_out_top))
            self.drawing.add(elm.Line().at(p_R6_bot).right().to(p_out_bot))
            self.drawing.add(elm.Dot().at(p_out_top))
            self.drawing.add(elm.Dot().at(p_out_bot))
        else:
            # R6 disabled - no resistor, no connections, completely disappears
            # Just connect bridge directly to output points
            self.drawing.add(elm.Line().at(p_bridge_R4_top).right().to(p_out_top))
            self.drawing.add(elm.Line().at(p_bridge_R5_bot).right().to(p_out_bot))
            self.drawing.add(elm.Dot().at(p_out_top))
            self.drawing.add(elm.Dot().at(p_out_bot))

        # Measurement Device across the bridge diagonal - properly oriented
        if self.model.meter_type == MeterType.VOLTMETER:
            meter = self.drawing.add(elm.MeterV().at(p_bridge_mid_left).right().to(p_bridge_mid_right).label("V"))
        else:
            meter = self.drawing.add(elm.MeterA().at(p_bridge_mid_left).right().to(p_bridge_mid_right).label("A"))

        self._store_element_position('METER', meter)

        # U2 REMOVED - No more fictional voltage labels in schematic

        # Add current measurement annotation with red arrow (exactly like reference)
        current_arrow_start = (p_bridge_mid_left[0] + 0.5, p_bridge_mid_left[1])
        current_arrow_end = (p_bridge_mid_right[0] - 0.5, p_bridge_mid_right[1])
        self.drawing.add(elm.Arrow().at(current_arrow_start).to(current_arrow_end).color('red'))
        self.drawing.add(elm.Label().at(current_arrow_start).label('I', loc='top').color('red'))

        # Add the calculated current value below the bridge
        bridge_current = self.calculated_results.get('bridge_current', 0.0)
        if abs(bridge_current) < 1e-6:
            current_label = f'I = {bridge_current*1e9:.0f} nA'
        elif abs(bridge_current) < 1e-3:
            current_label = f'I = {bridge_current*1e6:.0f} μA'
        elif abs(bridge_current) < 1:
            current_label = f'I = {bridge_current*1e3:.1f} mA'
        else:
            current_label = f'I = {bridge_current:.3f} A'
        result_pos = ((p_bridge_mid_left[0] + p_bridge_mid_right[0])/2, p_bridge_R3_bot[1] - 0.5)
        self.drawing.add(elm.Label().at(result_pos).label(current_label))

        # Add clean title exactly matching the reference
        title_pos = (p_bridge_R2_top[0] - 2, p_bridge_R2_top[1] + 1)
        self.drawing.add(elm.Label().at(title_pos).label('R₅ = 0 Ω: Beispiel'))

        # Draw the circuit with high DPI for HP Spectre 14
        ax = self.figure.add_subplot(111)

        # Use schemdraw's save method to get the figure
        import tempfile
        import os
        import matplotlib.image as mpimg

        # Save to temporary file with high DPI
        with tempfile.NamedTemporaryFile(suffix='.png', delete=False) as tmp:
            tmp_name = tmp.name

        try:
            self.drawing.save(tmp_name, dpi=150)  # Higher DPI for HP Spectre 14

            # Load and display the image
            img = mpimg.imread(tmp_name)
            ax.imshow(img)
            ax.set_title('H-Bridge Circuit Analysis', fontsize=16, fontweight='bold')
            ax.axis('off')
        finally:
            # Clean up temporary file
            if os.path.exists(tmp_name):
                os.unlink(tmp_name)

        # Refresh canvas
        self.canvas.draw()
    
    def _store_element_position(self, name: str, element):
        """Store element position for click detection"""
        if hasattr(element, 'center'):
            self.element_positions[name] = element.center
        elif hasattr(element, 'start') and hasattr(element, 'end'):
            # Use midpoint for line elements
            start = element.start
            end = element.end
            center = ((start[0] + end[0]) / 2, (start[1] + end[1]) / 2)
            self.element_positions[name] = center
    
    def _on_click(self, event):
        """Handle mouse clicks on the circuit"""
        if event.inaxes is None or self.click_callback is None:
            return
        
        click_x, click_y = event.xdata, event.ydata
        if click_x is None or click_y is None:
            return
        
        # Find closest element
        closest_element = None
        min_distance = float('inf')
        
        for name, pos in self.element_positions.items():
            distance = ((click_x - pos[0]) ** 2 + (click_y - pos[1]) ** 2) ** 0.5
            if distance < min_distance and distance < 1.0:  # Within reasonable click range
                min_distance = distance
                closest_element = name
        
        if closest_element and event.button == 3:  # Right click
            # Open configuration dialog immediately
            self._open_config_dialog(closest_element)
        elif closest_element and event.button == 1 and self.click_callback:  # Left click
            # Convert to screen coordinates for popup menu (legacy support)
            screen_x = int(event.x)
            screen_y = int(event.y)
            self.click_callback(closest_element, screen_x, screen_y)

    def _open_config_dialog(self, element_name):
        """Open the appropriate configuration dialog for the clicked element"""
        from gui_components import ResistorConfigDialog, VoltageSourceConfigDialog, MeterConfigDialog

        # Get the main window (traverse up the widget hierarchy)
        main_window = self.canvas
        while main_window.master:
            main_window = main_window.master

        try:
            if element_name in ['R1', 'R2', 'R3', 'R4', 'R5', 'R6']:
                # Open resistor configuration dialog
                resistor = self.model.get_resistor(element_name)
                dialog = ResistorConfigDialog(main_window, element_name, resistor, self._on_config_change)

            elif 'voltage' in element_name.lower() or element_name == 'U':
                # Open voltage source configuration dialog
                dialog = VoltageSourceConfigDialog(main_window, self.model, self._on_config_change)

            elif element_name == 'METER' or 'meter' in element_name.lower():
                # Open meter configuration dialog
                dialog = MeterConfigDialog(main_window, self.model, self._on_config_change)

            else:
                # Generic component - show info
                self._show_component_info(element_name)

        except Exception as e:
            print(f"Error opening configuration dialog for {element_name}: {e}")
            self._show_component_info(element_name)

    def _on_config_change(self):
        """Handle configuration changes - redraw circuit"""
        self.draw_circuit()
        self.canvas.draw()

    def _show_component_info(self, element_name):
        """Show information about a component"""
        import tkinter.messagebox as msgbox
        info = self.get_element_info(element_name)
        if info:
            info_text = f"Component: {info['name']}\nType: {info['type']}\n"
            if 'resistance' in info:
                info_text += f"Resistance: {info['resistance']} {info['unit']}\n"
            if 'enabled' in info:
                info_text += f"Enabled: {info['enabled']}\n"
            info_text += "\nRight-click to configure parameters."
        else:
            info_text = f"Component: {element_name}\nRight-click to configure specific parameters."

        msgbox.showinfo("Component Information", info_text)
    
    def update_circuit(self):
        """Redraw the circuit with current model state"""
        self.draw_circuit()

    def update_with_results(self, results: dict):
        """Update circuit display with calculated results"""
        # U2 REMOVED - No more fictional voltage tracking

        # Extract bridge current (could be from any bridge component)
        bridge_current = 0.0
        for comp in ['R2', 'R3', 'R4', 'R5']:
            if comp in results and 'current' in results[comp]:
                bridge_current = results[comp]['current']
                break
        self.calculated_results['bridge_current'] = bridge_current

        # Redraw circuit with updated values
        self.draw_circuit()
    
    def highlight_element(self, element_name: str, highlight: bool = True):
        """Highlight a specific element (for future enhancement)"""
        # This could be implemented to highlight elements during analysis
        pass
    
    def add_annotation(self, element_name: str, text: str):
        """Add text annotation to an element"""
        if element_name in self.element_positions:
            pos = self.element_positions[element_name]
            ax = self.figure.gca()
            ax.annotate(text, xy=pos, xytext=(10, 10), 
                       textcoords='offset points',
                       bbox=dict(boxstyle='round,pad=0.3', facecolor='yellow', alpha=0.7),
                       arrowprops=dict(arrowstyle='->', connectionstyle='arc3,rad=0'))
            self.canvas.draw()
    
    def clear_annotations(self):
        """Clear all annotations"""
        ax = self.figure.gca()
        # Remove all text annotations
        for child in ax.get_children():
            if hasattr(child, 'get_text'):
                child.remove()
        self.canvas.draw()
    
    def export_circuit(self, filename: str):
        """Export circuit diagram to file"""
        if self.drawing:
            self.drawing.save(filename)
    
    def get_element_info(self, element_name: str) -> Optional[Dict]:
        """Get information about a specific element"""
        if element_name.startswith('R') and element_name[1:].isdigit():
            resistor = self.model.get_resistor(element_name)
            if resistor:
                return {
                    'name': element_name,
                    'type': 'resistor',
                    'enabled': resistor.enabled,
                    'resistance': resistor.resistance.value,
                    'unit': resistor.resistance.unit
                }
        elif element_name == 'METER':
            return {
                'name': 'METER',
                'type': 'meter',
                'meter_type': self.model.meter_type.value,
                'measurement': self.model.meter_measurement.value,
                'unit': self.model.meter_measurement.unit
            }
        return None
