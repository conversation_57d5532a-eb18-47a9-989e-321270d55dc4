"""
Circuit Drawer for H-Bridge Analysis Application
Uses schemdraw to create interactive circuit diagrams.
"""

import schemdraw
import schemdraw.elements as elm
import matplotlib.pyplot as plt
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
import tkinter as tk
from typing import Dict, Tuple, Optional, Callable
from circuit_model import CircuitModel, MeterType


class CircuitDrawer:
    """<PERSON>les drawing and interaction with the H-bridge circuit"""
    
    def __init__(self, parent_frame: tk.Frame, model: CircuitModel):
        self.parent_frame = parent_frame
        self.model = model
        self.canvas = None
        self.figure = None
        self.drawing = None
        
        # Store element positions for click detection
        self.element_positions = {}
        self.click_callback = None
        
        # Initialize the drawing
        self.setup_canvas()
        self.draw_circuit()
    
    def setup_canvas(self):
        """Setup matplotlib canvas for schemdraw"""
        self.figure = plt.Figure(figsize=(12, 8), dpi=100)
        self.canvas = FigureCanvasTkAgg(self.figure, self.parent_frame)
        self.canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True)
        
        # Bind click events
        self.canvas.mpl_connect('button_press_event', self._on_click)
    
    def set_click_callback(self, callback: Callable[[str, int, int], None]):
        """Set callback for element clicks (element_name, x, y)"""
        self.click_callback = callback
    
    def draw_circuit(self):
        """Draw the complete H-bridge circuit"""
        self.figure.clear()

        # Create schemdraw drawing
        self.drawing = schemdraw.Drawing()

        # Start with voltage source
        vs = self.drawing.add(elm.SourceV().label(f'U\n{self.model.supply_voltage.value}{self.model.supply_voltage.unit}'))

        # Series resistor R1
        r1_enabled = self.model.get_resistor('R1').enabled
        if r1_enabled:
            r1 = self.drawing.add(elm.Resistor().right().label('R1'))
            self._store_element_position('R1', r1)
        else:
            # Draw wire bypass for disabled R1
            self.drawing.add(elm.Line().right().length(2))

        # Save position for bridge start
        bridge_start = self.drawing.here

        # Upper branch of H-bridge
        self.drawing.push()  # Save position

        # R2 (upper left)
        r2_enabled = self.model.get_resistor('R2').enabled
        if r2_enabled:
            r2 = self.drawing.add(elm.Resistor().up().label('R2'))
            self._store_element_position('R2', r2)
        else:
            self.drawing.add(elm.Line().up().length(2))

        # R4 (upper right)
        r4_enabled = self.model.get_resistor('R4').enabled
        if r4_enabled:
            r4 = self.drawing.add(elm.Resistor().right().label('R4'))
            self._store_element_position('R4', r4)
        else:
            self.drawing.add(elm.Line().right().length(2))

        # Save upper right position
        upper_right = self.drawing.here

        # Lower branch of H-bridge
        self.drawing.pop()  # Return to bridge start

        # R3 (lower left)
        r3_enabled = self.model.get_resistor('R3').enabled
        if r3_enabled:
            r3 = self.drawing.add(elm.Resistor().down().label('R3'))
            self._store_element_position('R3', r3)
        else:
            self.drawing.add(elm.Line().down().length(2))

        # R5 (lower right)
        r5_enabled = self.model.get_resistor('R5').enabled
        if r5_enabled:
            r5 = self.drawing.add(elm.Resistor().right().label('R5'))
            self._store_element_position('R5', r5)
        else:
            self.drawing.add(elm.Line().right().length(2))

        # Connect to upper right
        self.drawing.add(elm.Line().to(upper_right))

        # Add measurement device in the center of bridge
        self.drawing.push()
        # Go to center of bridge
        center_x = (bridge_start[0] + upper_right[0]) / 2
        center_y = (bridge_start[1] + upper_right[1]) / 2

        if self.model.meter_type == MeterType.VOLTMETER:
            meter = self.drawing.add(elm.MeterV().at((center_x, center_y)).label('V'))
        else:
            meter = self.drawing.add(elm.MeterA().at((center_x, center_y)).label('A'))

        self._store_element_position('METER', meter)
        self.drawing.pop()

        # Parallel resistor R6
        self.drawing.push()
        r6_enabled = self.model.get_resistor('R6').enabled
        if r6_enabled:
            r6 = self.drawing.add(elm.Resistor().down().at(upper_right).label('R6'))
            self._store_element_position('R6', r6)
        else:
            # Draw wire bypass
            self.drawing.add(elm.Line().down().at(upper_right).length(2))

        # Connect back to voltage source
        self.drawing.add(elm.Line().left().to(vs.start))

        # Draw the circuit directly to our figure
        ax = self.figure.add_subplot(111)

        # Use schemdraw's save method to get the figure
        import tempfile
        import os
        import matplotlib.image as mpimg

        # Save to temporary file
        with tempfile.NamedTemporaryFile(suffix='.png', delete=False) as tmp:
            tmp_name = tmp.name

        try:
            self.drawing.save(tmp_name, dpi=100)

            # Load and display the image
            img = mpimg.imread(tmp_name)
            ax.imshow(img)
            ax.set_title('H-Bridge Circuit Analysis', fontsize=14, fontweight='bold')
            ax.axis('off')
        finally:
            # Clean up temporary file
            if os.path.exists(tmp_name):
                os.unlink(tmp_name)

        # Refresh canvas
        self.canvas.draw()
    
    def _store_element_position(self, name: str, element):
        """Store element position for click detection"""
        if hasattr(element, 'center'):
            self.element_positions[name] = element.center
        elif hasattr(element, 'start') and hasattr(element, 'end'):
            # Use midpoint for line elements
            start = element.start
            end = element.end
            center = ((start[0] + end[0]) / 2, (start[1] + end[1]) / 2)
            self.element_positions[name] = center
    
    def _on_click(self, event):
        """Handle mouse clicks on the circuit"""
        if event.inaxes is None or self.click_callback is None:
            return
        
        click_x, click_y = event.xdata, event.ydata
        if click_x is None or click_y is None:
            return
        
        # Find closest element
        closest_element = None
        min_distance = float('inf')
        
        for name, pos in self.element_positions.items():
            distance = ((click_x - pos[0]) ** 2 + (click_y - pos[1]) ** 2) ** 0.5
            if distance < min_distance and distance < 1.0:  # Within reasonable click range
                min_distance = distance
                closest_element = name
        
        if closest_element and event.button == 3:  # Right click
            # Convert to screen coordinates for popup menu
            screen_x = int(event.x)
            screen_y = int(event.y)
            self.click_callback(closest_element, screen_x, screen_y)
    
    def update_circuit(self):
        """Redraw the circuit with current model state"""
        self.draw_circuit()
    
    def highlight_element(self, element_name: str, highlight: bool = True):
        """Highlight a specific element (for future enhancement)"""
        # This could be implemented to highlight elements during analysis
        pass
    
    def add_annotation(self, element_name: str, text: str):
        """Add text annotation to an element"""
        if element_name in self.element_positions:
            pos = self.element_positions[element_name]
            ax = self.figure.gca()
            ax.annotate(text, xy=pos, xytext=(10, 10), 
                       textcoords='offset points',
                       bbox=dict(boxstyle='round,pad=0.3', facecolor='yellow', alpha=0.7),
                       arrowprops=dict(arrowstyle='->', connectionstyle='arc3,rad=0'))
            self.canvas.draw()
    
    def clear_annotations(self):
        """Clear all annotations"""
        ax = self.figure.gca()
        # Remove all text annotations
        for child in ax.get_children():
            if hasattr(child, 'get_text'):
                child.remove()
        self.canvas.draw()
    
    def export_circuit(self, filename: str):
        """Export circuit diagram to file"""
        if self.drawing:
            self.drawing.save(filename)
    
    def get_element_info(self, element_name: str) -> Optional[Dict]:
        """Get information about a specific element"""
        if element_name.startswith('R') and element_name[1:].isdigit():
            resistor = self.model.get_resistor(element_name)
            if resistor:
                return {
                    'name': element_name,
                    'type': 'resistor',
                    'enabled': resistor.enabled,
                    'resistance': resistor.resistance.value,
                    'unit': resistor.resistance.unit
                }
        elif element_name == 'METER':
            return {
                'name': 'METER',
                'type': 'meter',
                'meter_type': self.model.meter_type.value,
                'measurement': self.model.meter_measurement.value,
                'unit': self.model.meter_measurement.unit
            }
        return None
