"""
Circuit Drawer for H-Bridge Analysis Application
Uses schemdraw to create interactive circuit diagrams.
"""

import schemdraw
import schemdraw.elements as elm
import matplotlib.pyplot as plt
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
import tkinter as tk
from typing import Dict, Tuple, Optional, Callable
from circuit_model import CircuitModel, MeterType


class CircuitDrawer:
    """<PERSON><PERSON> drawing and interaction with the H-bridge circuit"""
    
    def __init__(self, parent_frame: tk.Frame, model: CircuitModel):
        self.parent_frame = parent_frame
        self.model = model
        self.canvas = None
        self.figure = None
        self.drawing = None
        
        # Store element positions for click detection
        self.element_positions = {}
        self.click_callback = None
        
        # Initialize the drawing
        self.setup_canvas()
        self.draw_circuit()
    
    def setup_canvas(self):
        """Setup matplotlib canvas optimized for HP Spectre 14"""
        # Optimize figure size and DPI for HP Spectre 14 high-resolution display
        self.figure = plt.Figure(figsize=(14, 10), dpi=120)
        self.canvas = FigureCanvasTkAgg(self.figure, self.parent_frame)
        self.canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True)

        # Configure for high DPI display
        self.figure.patch.set_facecolor('white')

        # Bind click events (both left and right click)
        self.canvas.mpl_connect('button_press_event', self._on_click)
    
    def set_click_callback(self, callback: Callable[[str, int, int], None]):
        """Set callback for element clicks (element_name, x, y)"""
        self.click_callback = callback
    
    def draw_circuit(self):
        """Draw clean H-bridge circuit exactly matching the reference topology"""
        self.figure.clear()

        # Create schemdraw drawing with clean, professional appearance
        self.drawing = schemdraw.Drawing()

        # Start with voltage source (left side, vertical) - exactly like reference
        vs = self.drawing.add(elm.SourceV().up().label(f'U\n{self.model.supply_voltage.value}V'))
        vs_top = vs.end
        vs_bottom = vs.start

        # Series resistor R1 (horizontal from voltage source top)
        r1_enabled = self.model.get_resistor('R1').enabled
        if r1_enabled:
            r1 = self.drawing.add(elm.Resistor().at(vs_top).right().label('R₁\n180'))
            self._store_element_position('R1', r1)
            after_r1 = r1.end
        else:
            # Draw clean wire bypass for disabled R1
            r1_wire = self.drawing.add(elm.Line().at(vs_top).right().length(2))
            after_r1 = r1_wire.end

        # Top rail - clean horizontal line to bridge
        top_rail_1 = self.drawing.add(elm.Line().at(after_r1).right().length(1))
        bridge_top_left = top_rail_1.end

        # Continue top rail across bridge
        top_rail_2 = self.drawing.add(elm.Line().at(bridge_top_left).right().length(2))
        bridge_top_right = top_rail_2.end

        # Continue top rail to output
        top_rail_3 = self.drawing.add(elm.Line().at(bridge_top_right).right().length(2))
        output_top = top_rail_3.end

        # R2 - left vertical branch of bridge
        r2 = self.drawing.add(elm.Resistor().at(bridge_top_left).down().label('R₂\n100'))
        self._store_element_position('R2', r2)
        bridge_middle_left = r2.end

        # R4 - right vertical branch of bridge
        r4 = self.drawing.add(elm.Resistor().at(bridge_top_right).down().label('R₄\n22'))
        self._store_element_position('R4', r4)
        bridge_middle_right = r4.end

        # R3 - left bottom branch of bridge
        r3 = self.drawing.add(elm.Resistor().at(bridge_middle_left).down().label('R₃\n39'))
        self._store_element_position('R3', r3)
        bridge_bottom_left = r3.end

        # R5 - right bottom branch (short circuit for example)
        r5_enabled = self.model.get_resistor('R5').enabled
        if r5_enabled:
            r5 = self.drawing.add(elm.Resistor().at(bridge_middle_right).down().label('R₅\n39'))
            self._store_element_position('R5', r5)
            bridge_bottom_right = r5.end
        else:
            # R5 = 0Ω, draw clean short circuit wire
            r5_short = self.drawing.add(elm.Line().at(bridge_middle_right).down().length(2))
            bridge_bottom_right = r5_short.end
            # Add label for short circuit
            self.drawing.add(elm.Label().at(bridge_middle_right).label('R₅ = 0Ω', loc='right'))

        # Bottom rail - clean horizontal connection
        bottom_rail = self.drawing.add(elm.Line().at(bridge_bottom_left).right().to(bridge_bottom_right))

        # R6 - parallel load resistor (clean vertical connection)
        r6_enabled = self.model.get_resistor('R6').enabled
        if r6_enabled:
            r6 = self.drawing.add(elm.Resistor().at(output_top).down().label('R₆\n39'))
            self._store_element_position('R6', r6)
            output_bottom = r6.end
        else:
            # Clean wire for disabled R6
            r6_wire = self.drawing.add(elm.Line().at(output_top).down().length(4))
            output_bottom = r6_wire.end

        # Connect output bottom to bridge bottom rail
        output_connection = self.drawing.add(elm.Line().at(output_bottom).left().to(bridge_bottom_right))

        # Close the circuit - connect bottom rail back to voltage source
        return_connection = self.drawing.add(elm.Line().at(bridge_bottom_left).left().to(vs_bottom))

        # Add measurement device across the bridge (between middle points) - clean placement
        if self.model.meter_type == MeterType.VOLTMETER:
            meter = self.drawing.add(elm.MeterV().at(bridge_middle_left).to(bridge_middle_right).label('V'))
        else:
            meter = self.drawing.add(elm.MeterA().at(bridge_middle_left).to(bridge_middle_right).label('A'))

        self._store_element_position('METER', meter)

        # Add clean output voltage measurement point
        self.drawing.add(elm.Dot().at(output_top))
        self.drawing.add(elm.Label().at(output_top).label('U₂\n0.1V', loc='right'))

        # Add current measurement annotation (clean positioning)
        current_label_pos = (bridge_middle_left[0], bridge_middle_left[1] - 0.5)
        self.drawing.add(elm.Label().at(current_label_pos).label('I = -559 μA'))

        # Add clean title matching the reference
        title_pos = (bridge_top_left[0] + 1, bridge_top_left[1] + 1.5)
        self.drawing.add(elm.Label().at(title_pos).label('R₅ = 0 Ω: Beispiel'))

        # Draw the circuit with high DPI for HP Spectre 14
        ax = self.figure.add_subplot(111)

        # Use schemdraw's save method to get the figure
        import tempfile
        import os
        import matplotlib.image as mpimg

        # Save to temporary file with high DPI
        with tempfile.NamedTemporaryFile(suffix='.png', delete=False) as tmp:
            tmp_name = tmp.name

        try:
            self.drawing.save(tmp_name, dpi=150)  # Higher DPI for HP Spectre 14

            # Load and display the image
            img = mpimg.imread(tmp_name)
            ax.imshow(img)
            ax.set_title('H-Bridge Circuit Analysis', fontsize=16, fontweight='bold')
            ax.axis('off')
        finally:
            # Clean up temporary file
            if os.path.exists(tmp_name):
                os.unlink(tmp_name)

        # Refresh canvas
        self.canvas.draw()
    
    def _store_element_position(self, name: str, element):
        """Store element position for click detection"""
        if hasattr(element, 'center'):
            self.element_positions[name] = element.center
        elif hasattr(element, 'start') and hasattr(element, 'end'):
            # Use midpoint for line elements
            start = element.start
            end = element.end
            center = ((start[0] + end[0]) / 2, (start[1] + end[1]) / 2)
            self.element_positions[name] = center
    
    def _on_click(self, event):
        """Handle mouse clicks on the circuit"""
        if event.inaxes is None or self.click_callback is None:
            return
        
        click_x, click_y = event.xdata, event.ydata
        if click_x is None or click_y is None:
            return
        
        # Find closest element
        closest_element = None
        min_distance = float('inf')
        
        for name, pos in self.element_positions.items():
            distance = ((click_x - pos[0]) ** 2 + (click_y - pos[1]) ** 2) ** 0.5
            if distance < min_distance and distance < 1.0:  # Within reasonable click range
                min_distance = distance
                closest_element = name
        
        if closest_element and event.button == 3:  # Right click
            # Convert to screen coordinates for popup menu
            screen_x = int(event.x)
            screen_y = int(event.y)
            self.click_callback(closest_element, screen_x, screen_y)
    
    def update_circuit(self):
        """Redraw the circuit with current model state"""
        self.draw_circuit()
    
    def highlight_element(self, element_name: str, highlight: bool = True):
        """Highlight a specific element (for future enhancement)"""
        # This could be implemented to highlight elements during analysis
        pass
    
    def add_annotation(self, element_name: str, text: str):
        """Add text annotation to an element"""
        if element_name in self.element_positions:
            pos = self.element_positions[element_name]
            ax = self.figure.gca()
            ax.annotate(text, xy=pos, xytext=(10, 10), 
                       textcoords='offset points',
                       bbox=dict(boxstyle='round,pad=0.3', facecolor='yellow', alpha=0.7),
                       arrowprops=dict(arrowstyle='->', connectionstyle='arc3,rad=0'))
            self.canvas.draw()
    
    def clear_annotations(self):
        """Clear all annotations"""
        ax = self.figure.gca()
        # Remove all text annotations
        for child in ax.get_children():
            if hasattr(child, 'get_text'):
                child.remove()
        self.canvas.draw()
    
    def export_circuit(self, filename: str):
        """Export circuit diagram to file"""
        if self.drawing:
            self.drawing.save(filename)
    
    def get_element_info(self, element_name: str) -> Optional[Dict]:
        """Get information about a specific element"""
        if element_name.startswith('R') and element_name[1:].isdigit():
            resistor = self.model.get_resistor(element_name)
            if resistor:
                return {
                    'name': element_name,
                    'type': 'resistor',
                    'enabled': resistor.enabled,
                    'resistance': resistor.resistance.value,
                    'unit': resistor.resistance.unit
                }
        elif element_name == 'METER':
            return {
                'name': 'METER',
                'type': 'meter',
                'meter_type': self.model.meter_type.value,
                'measurement': self.model.meter_measurement.value,
                'unit': self.model.meter_measurement.unit
            }
        return None
