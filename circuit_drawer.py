"""
Circuit Drawer for H-Bridge Analysis Application
Uses schemdraw to create interactive circuit diagrams.
"""

import schemdraw
import schemdraw.elements as elm
import matplotlib.pyplot as plt
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
import tkinter as tk
from typing import Dict, Tuple, Optional, Callable
from circuit_model import CircuitModel, MeterType


class CircuitDrawer:
    """<PERSON><PERSON> drawing and interaction with the H-bridge circuit"""
    
    def __init__(self, parent_frame: tk.Frame, model: CircuitModel):
        self.parent_frame = parent_frame
        self.model = model
        self.canvas = None
        self.figure = None
        self.drawing = None
        
        # Store element positions for click detection
        self.element_positions = {}
        self.click_callback = None
        
        # Initialize the drawing
        self.setup_canvas()
        self.draw_circuit()
    
    def setup_canvas(self):
        """Setup matplotlib canvas optimized for HP Spectre 14"""
        # Optimize figure size and DPI for HP Spectre 14 high-resolution display
        self.figure = plt.Figure(figsize=(14, 10), dpi=120)
        self.canvas = FigureCanvasTkAgg(self.figure, self.parent_frame)
        self.canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True)

        # Configure for high DPI display
        self.figure.patch.set_facecolor('white')

        # Bind click events (both left and right click)
        self.canvas.mpl_connect('button_press_event', self._on_click)
    
    def set_click_callback(self, callback: Callable[[str, int, int], None]):
        """Set callback for element clicks (element_name, x, y)"""
        self.click_callback = callback
    
    def draw_circuit(self):
        """Draw the H-bridge circuit matching the reference topology exactly"""
        self.figure.clear()

        # Create schemdraw drawing with higher DPI for HP Spectre 14
        self.drawing = schemdraw.Drawing()

        # Start with voltage source (left side, vertical) - matching reference exactly
        vs = self.drawing.add(elm.SourceV().up().label(f'U\n{self.model.supply_voltage.value}V'))

        # Series resistor R1 (horizontal, after voltage source)
        r1_enabled = self.model.get_resistor('R1').enabled
        if r1_enabled:
            r1 = self.drawing.add(elm.Resistor().right().label('R₁\n180'))
            self._store_element_position('R1', r1)
        else:
            # Draw wire bypass for disabled R1
            self.drawing.add(elm.Line().right().length(2))

        # Top rail - horizontal line to first junction
        self.drawing.add(elm.Line().right().length(1))
        top_left_junction = self.drawing.here

        # Continue top rail across the bridge
        self.drawing.add(elm.Line().right().length(3))
        top_middle_junction = self.drawing.here

        # Continue to right side
        self.drawing.add(elm.Line().right().length(3))
        top_right_junction = self.drawing.here

        # Final extension to output terminal
        self.drawing.add(elm.Line().right().length(1))
        output_terminal = self.drawing.here

        # R2 - top left branch (vertical down from top-left junction)
        r2 = self.drawing.add(elm.Resistor().at(top_left_junction).down().label('R₂\n100'))
        self._store_element_position('R2', r2)
        left_middle_junction = r2.end

        # R4 - top right branch (vertical down from top-middle junction)
        r4 = self.drawing.add(elm.Resistor().at(top_middle_junction).down().label('R₄\n22'))
        self._store_element_position('R4', r4)
        right_middle_junction = r4.end

        # R3 - bottom left branch (vertical down from left middle)
        r3 = self.drawing.add(elm.Resistor().at(left_middle_junction).down().label('R₃\n39'))
        self._store_element_position('R3', r3)
        bottom_left_junction = r3.end

        # R5 - bottom right branch (vertical down from right middle)
        r5_enabled = self.model.get_resistor('R5').enabled
        if r5_enabled:
            r5 = self.drawing.add(elm.Resistor().at(right_middle_junction).down().label('R₅\n39'))
            self._store_element_position('R5', r5)
            bottom_right_junction = r5.end
        else:
            # R5 = 0, draw wire (short circuit) - this is the key for the example
            r5_wire = self.drawing.add(elm.Line().at(right_middle_junction).down().length(2))
            bottom_right_junction = r5_wire.end

        # Bottom rail - connect bottom junctions
        self.drawing.add(elm.Line().at(bottom_left_junction).to(bottom_right_junction))

        # R6 - parallel load resistor (from top-right to bottom-right)
        r6_enabled = self.model.get_resistor('R6').enabled
        if r6_enabled:
            r6 = self.drawing.add(elm.Resistor().at(top_right_junction).down().label('R₆\n39'))
            self._store_element_position('R6', r6)
            # Connect R6 bottom to bottom rail
            self.drawing.add(elm.Line().at(r6.end).to(bottom_right_junction))
        else:
            # Draw wire bypass for disabled R6
            self.drawing.add(elm.Line().at(top_right_junction).down().to(bottom_right_junction))

        # Connect bottom rail back to voltage source
        self.drawing.add(elm.Line().at(bottom_left_junction).left().to(vs.start))

        # Add measurement device across the bridge (between middle points)
        if self.model.meter_type == MeterType.VOLTMETER:
            meter = self.drawing.add(elm.MeterV().at(left_middle_junction).to(right_middle_junction).label('V'))
        else:
            meter = self.drawing.add(elm.MeterA().at(left_middle_junction).to(right_middle_junction).label('A'))

        self._store_element_position('METER', meter)

        # Add output voltage measurement point
        self.drawing.add(elm.Dot().at(top_right_junction))
        self.drawing.add(elm.Label().at(top_right_junction).label('U₂\n0.1V', loc='right'))

        # Add current measurement annotation (showing the example current)
        self.drawing.add(elm.Label().at(left_middle_junction).label('I = -559 μA', loc='bottom'))

        # Add title matching the reference (simplified for schemdraw compatibility)
        self.drawing.add(elm.Label().at((0, 6)).label('R₅ = 0 Ω: Beispiel'))

        # Draw the circuit with high DPI for HP Spectre 14
        ax = self.figure.add_subplot(111)

        # Use schemdraw's save method to get the figure
        import tempfile
        import os
        import matplotlib.image as mpimg

        # Save to temporary file with high DPI
        with tempfile.NamedTemporaryFile(suffix='.png', delete=False) as tmp:
            tmp_name = tmp.name

        try:
            self.drawing.save(tmp_name, dpi=150)  # Higher DPI for HP Spectre 14

            # Load and display the image
            img = mpimg.imread(tmp_name)
            ax.imshow(img)
            ax.set_title('H-Bridge Circuit Analysis', fontsize=16, fontweight='bold')
            ax.axis('off')
        finally:
            # Clean up temporary file
            if os.path.exists(tmp_name):
                os.unlink(tmp_name)

        # Refresh canvas
        self.canvas.draw()
    
    def _store_element_position(self, name: str, element):
        """Store element position for click detection"""
        if hasattr(element, 'center'):
            self.element_positions[name] = element.center
        elif hasattr(element, 'start') and hasattr(element, 'end'):
            # Use midpoint for line elements
            start = element.start
            end = element.end
            center = ((start[0] + end[0]) / 2, (start[1] + end[1]) / 2)
            self.element_positions[name] = center
    
    def _on_click(self, event):
        """Handle mouse clicks on the circuit"""
        if event.inaxes is None or self.click_callback is None:
            return
        
        click_x, click_y = event.xdata, event.ydata
        if click_x is None or click_y is None:
            return
        
        # Find closest element
        closest_element = None
        min_distance = float('inf')
        
        for name, pos in self.element_positions.items():
            distance = ((click_x - pos[0]) ** 2 + (click_y - pos[1]) ** 2) ** 0.5
            if distance < min_distance and distance < 1.0:  # Within reasonable click range
                min_distance = distance
                closest_element = name
        
        if closest_element and event.button == 3:  # Right click
            # Convert to screen coordinates for popup menu
            screen_x = int(event.x)
            screen_y = int(event.y)
            self.click_callback(closest_element, screen_x, screen_y)
    
    def update_circuit(self):
        """Redraw the circuit with current model state"""
        self.draw_circuit()
    
    def highlight_element(self, element_name: str, highlight: bool = True):
        """Highlight a specific element (for future enhancement)"""
        # This could be implemented to highlight elements during analysis
        pass
    
    def add_annotation(self, element_name: str, text: str):
        """Add text annotation to an element"""
        if element_name in self.element_positions:
            pos = self.element_positions[element_name]
            ax = self.figure.gca()
            ax.annotate(text, xy=pos, xytext=(10, 10), 
                       textcoords='offset points',
                       bbox=dict(boxstyle='round,pad=0.3', facecolor='yellow', alpha=0.7),
                       arrowprops=dict(arrowstyle='->', connectionstyle='arc3,rad=0'))
            self.canvas.draw()
    
    def clear_annotations(self):
        """Clear all annotations"""
        ax = self.figure.gca()
        # Remove all text annotations
        for child in ax.get_children():
            if hasattr(child, 'get_text'):
                child.remove()
        self.canvas.draw()
    
    def export_circuit(self, filename: str):
        """Export circuit diagram to file"""
        if self.drawing:
            self.drawing.save(filename)
    
    def get_element_info(self, element_name: str) -> Optional[Dict]:
        """Get information about a specific element"""
        if element_name.startswith('R') and element_name[1:].isdigit():
            resistor = self.model.get_resistor(element_name)
            if resistor:
                return {
                    'name': element_name,
                    'type': 'resistor',
                    'enabled': resistor.enabled,
                    'resistance': resistor.resistance.value,
                    'unit': resistor.resistance.unit
                }
        elif element_name == 'METER':
            return {
                'name': 'METER',
                'type': 'meter',
                'meter_type': self.model.meter_type.value,
                'measurement': self.model.meter_measurement.value,
                'unit': self.model.meter_measurement.unit
            }
        return None
