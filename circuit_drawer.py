"""
Circuit Drawer for H-Bridge Analysis Application
Uses schemdraw to create interactive circuit diagrams.
"""

import schemdraw
import schemdraw.elements as elm
import matplotlib.pyplot as plt
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
import tkinter as tk
from typing import Dict, Tuple, Optional, Callable
from circuit_model import CircuitModel, MeterType


class CircuitDrawer:
    """<PERSON><PERSON> drawing and interaction with the H-bridge circuit"""
    
    def __init__(self, parent_frame: tk.Frame, model: CircuitModel):
        self.parent_frame = parent_frame
        self.model = model
        self.canvas = None
        self.figure = None
        self.drawing = None
        
        # Store element positions for click detection
        self.element_positions = {}
        self.click_callback = None
        
        # Initialize the drawing
        self.setup_canvas()
        self.draw_circuit()
    
    def setup_canvas(self):
        """Setup matplotlib canvas for schemdraw"""
        self.figure = plt.Figure(figsize=(12, 8), dpi=100)
        self.canvas = FigureCanvasTkAgg(self.figure, self.parent_frame)
        self.canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True)
        
        # Bind click events
        self.canvas.mpl_connect('button_press_event', self._on_click)
    
    def set_click_callback(self, callback: Callable[[str, int, int], None]):
        """Set callback for element clicks (element_name, x, y)"""
        self.click_callback = callback
    
    def draw_circuit(self):
        """Draw the complete H-bridge circuit with correct topology"""
        self.figure.clear()

        # Create schemdraw drawing
        self.drawing = schemdraw.Drawing()

        # Start with voltage source (vertical, pointing up)
        vs = self.drawing.add(elm.SourceV().up().label(f'U\n{self.model.supply_voltage.value}{self.model.supply_voltage.unit}'))

        # Series resistor R1 (optional)
        r1_enabled = self.model.get_resistor('R1').enabled
        if r1_enabled:
            r1 = self.drawing.add(elm.Resistor().right().label('R1'))
            self._store_element_position('R1', r1)
        else:
            # Draw wire bypass for disabled R1
            self.drawing.add(elm.Line().right().length(2))

        # Create H-bridge structure
        # Top rail
        self.drawing.add(elm.Line().right().length(1))

        # Save positions for bridge connections
        self.drawing.push()  # Save top-left position

        # R2 (top-left of bridge)
        r2 = self.drawing.add(elm.Resistor().down().label('R2'))
        self._store_element_position('R2', r2)

        # R3 (bottom-left of bridge)
        r3 = self.drawing.add(elm.Resistor().down().label('R3'))
        self._store_element_position('R3', r3)

        # Bottom rail start
        bottom_start = self.drawing.here

        # Go back to top and continue top rail
        self.drawing.pop()
        self.drawing.add(elm.Line().right().length(2))

        # Save top-right position
        self.drawing.push()

        # R4 (top-right of bridge)
        r4 = self.drawing.add(elm.Resistor().down().label('R4'))
        self._store_element_position('R4', r4)

        # R5 (bottom-right of bridge)
        r5 = self.drawing.add(elm.Resistor().down().label('R5'))
        self._store_element_position('R5', r5)

        # Connect to bottom rail
        self.drawing.add(elm.Line().to(bottom_start))

        # Go back to top-right for R6 connection
        self.drawing.pop()
        self.drawing.add(elm.Line().right().length(1))

        # Parallel resistor R6 (optional)
        r6_enabled = self.model.get_resistor('R6').enabled
        if r6_enabled:
            self.drawing.push()
            r6 = self.drawing.add(elm.Resistor().down().label('R6'))
            self._store_element_position('R6', r6)
            self.drawing.pop()
        else:
            # Draw wire bypass for disabled R6
            self.drawing.push()
            self.drawing.add(elm.Line().down().length(4))
            self.drawing.pop()

        # Connect back to voltage source
        self.drawing.add(elm.Line().down().length(4))
        self.drawing.add(elm.Line().left().to(vs.start))

        # Add measurement device across the bridge
        # Calculate bridge measurement points
        bridge_left = (r2.end[0], (r2.end[1] + r3.start[1]) / 2)
        bridge_right = (r4.end[0], (r4.end[1] + r5.start[1]) / 2)

        if self.model.meter_type == MeterType.VOLTMETER:
            meter = self.drawing.add(elm.MeterV().at(bridge_left).to(bridge_right).label('V'))
        else:
            meter = self.drawing.add(elm.MeterA().at(bridge_left).to(bridge_right).label('A'))

        self._store_element_position('METER', meter)

        # Draw the circuit directly to our figure
        ax = self.figure.add_subplot(111)

        # Use schemdraw's save method to get the figure
        import tempfile
        import os
        import matplotlib.image as mpimg

        # Save to temporary file
        with tempfile.NamedTemporaryFile(suffix='.png', delete=False) as tmp:
            tmp_name = tmp.name

        try:
            self.drawing.save(tmp_name, dpi=100)

            # Load and display the image
            img = mpimg.imread(tmp_name)
            ax.imshow(img)
            ax.set_title('H-Bridge Circuit Analysis', fontsize=14, fontweight='bold')
            ax.axis('off')
        finally:
            # Clean up temporary file
            if os.path.exists(tmp_name):
                os.unlink(tmp_name)

        # Refresh canvas
        self.canvas.draw()
    
    def _store_element_position(self, name: str, element):
        """Store element position for click detection"""
        if hasattr(element, 'center'):
            self.element_positions[name] = element.center
        elif hasattr(element, 'start') and hasattr(element, 'end'):
            # Use midpoint for line elements
            start = element.start
            end = element.end
            center = ((start[0] + end[0]) / 2, (start[1] + end[1]) / 2)
            self.element_positions[name] = center
    
    def _on_click(self, event):
        """Handle mouse clicks on the circuit"""
        if event.inaxes is None or self.click_callback is None:
            return
        
        click_x, click_y = event.xdata, event.ydata
        if click_x is None or click_y is None:
            return
        
        # Find closest element
        closest_element = None
        min_distance = float('inf')
        
        for name, pos in self.element_positions.items():
            distance = ((click_x - pos[0]) ** 2 + (click_y - pos[1]) ** 2) ** 0.5
            if distance < min_distance and distance < 1.0:  # Within reasonable click range
                min_distance = distance
                closest_element = name
        
        if closest_element and event.button == 3:  # Right click
            # Convert to screen coordinates for popup menu
            screen_x = int(event.x)
            screen_y = int(event.y)
            self.click_callback(closest_element, screen_x, screen_y)
    
    def update_circuit(self):
        """Redraw the circuit with current model state"""
        self.draw_circuit()
    
    def highlight_element(self, element_name: str, highlight: bool = True):
        """Highlight a specific element (for future enhancement)"""
        # This could be implemented to highlight elements during analysis
        pass
    
    def add_annotation(self, element_name: str, text: str):
        """Add text annotation to an element"""
        if element_name in self.element_positions:
            pos = self.element_positions[element_name]
            ax = self.figure.gca()
            ax.annotate(text, xy=pos, xytext=(10, 10), 
                       textcoords='offset points',
                       bbox=dict(boxstyle='round,pad=0.3', facecolor='yellow', alpha=0.7),
                       arrowprops=dict(arrowstyle='->', connectionstyle='arc3,rad=0'))
            self.canvas.draw()
    
    def clear_annotations(self):
        """Clear all annotations"""
        ax = self.figure.gca()
        # Remove all text annotations
        for child in ax.get_children():
            if hasattr(child, 'get_text'):
                child.remove()
        self.canvas.draw()
    
    def export_circuit(self, filename: str):
        """Export circuit diagram to file"""
        if self.drawing:
            self.drawing.save(filename)
    
    def get_element_info(self, element_name: str) -> Optional[Dict]:
        """Get information about a specific element"""
        if element_name.startswith('R') and element_name[1:].isdigit():
            resistor = self.model.get_resistor(element_name)
            if resistor:
                return {
                    'name': element_name,
                    'type': 'resistor',
                    'enabled': resistor.enabled,
                    'resistance': resistor.resistance.value,
                    'unit': resistor.resistance.unit
                }
        elif element_name == 'METER':
            return {
                'name': 'METER',
                'type': 'meter',
                'meter_type': self.model.meter_type.value,
                'measurement': self.model.meter_measurement.value,
                'unit': self.model.meter_measurement.unit
            }
        return None
