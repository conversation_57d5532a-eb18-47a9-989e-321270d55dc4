"""
Circuit Model for H-Bridge Analysis Application
Manages the state and configuration of all circuit components.
"""

from enum import Enum
from dataclasses import dataclass
from typing import Dict, Optional, Any


class ParameterState(Enum):
    """State of a parameter in the analysis"""
    INACTIVE = "white"      # Not used in calculation
    GIVEN = "blue"          # Known value (input)
    UNKNOWN = "green"       # To be calculated (output)


class MeterType(Enum):
    """Type of measurement device"""
    VOLTMETER = "voltmeter"
    AMMETER = "ammeter"


@dataclass
class Parameter:
    """Represents a single electrical parameter"""
    value: float = 0.0
    state: ParameterState = ParameterState.INACTIVE
    unit: str = ""
    
    def is_given(self) -> bool:
        return self.state == ParameterState.GIVEN
    
    def is_unknown(self) -> bool:
        return self.state == ParameterState.UNKNOWN
    
    def is_active(self) -> bool:
        return self.state != ParameterState.INACTIVE


@dataclass
class ResistorState:
    """State of a resistor in the circuit"""
    enabled: bool = True
    resistance: Parameter = None
    voltage: Parameter = None
    current: Parameter = None
    power: Parameter = None
    
    def __post_init__(self):
        if self.resistance is None:
            self.resistance = Parameter(unit="Ω")
        if self.voltage is None:
            self.voltage = Parameter(unit="V")
        if self.current is None:
            self.current = Parameter(unit="A")
        if self.power is None:
            self.power = Parameter(unit="W")
    
    def get_parameter(self, param_type: str) -> Parameter:
        """Get parameter by type name"""
        return getattr(self, param_type.lower())
    
    def has_given_values(self) -> bool:
        """Check if any parameters are marked as given"""
        return any(param.is_given() for param in 
                  [self.resistance, self.voltage, self.current, self.power])
    
    def has_unknown_values(self) -> bool:
        """Check if any parameters are marked as unknown"""
        return any(param.is_unknown() for param in 
                  [self.resistance, self.voltage, self.current, self.power])


class CircuitModel:
    """Main model for the H-bridge circuit"""
    
    def __init__(self):
        # Initialize all resistors (R1-R6) with default values matching reference example
        self.resistors: Dict[str, ResistorState] = {
            f"R{i}": ResistorState() for i in range(1, 7)
        }

        # Set default resistance values to match the reference example
        default_values = {
            'R1': 180.0,  # Series resistor
            'R2': 100.0,  # Bridge top-left
            'R3': 39.0,   # Bridge bottom-left
            'R4': 22.0,   # Bridge top-right
            'R5': 39.0,   # Bridge bottom-right (will be set to 0 for example)
            'R6': 39.0,   # Parallel load
        }

        for name, value in default_values.items():
            self.resistors[name].resistance.value = value
            self.resistors[name].resistance.state = ParameterState.GIVEN

        # Ensure core H-bridge resistors (R2-R5) are always enabled
        for i in range(2, 6):
            self.resistors[f"R{i}"].enabled = True
            print(f"DEBUG: Setting {f'R{i}'} enabled = True")

        # For the reference example, set R5 = 0 (short circuit)
        self.resistors['R5'].resistance.value = 0.0
        self.resistors['R5'].enabled = False  # Disabled means short circuit
        print(f"DEBUG: R5 set to disabled (short circuit)")

        # CRITICAL: Ensure R4 is always enabled
        self.resistors['R4'].enabled = True
        print(f"DEBUG: R4 forced enabled = {self.resistors['R4'].enabled}, value = {self.resistors['R4'].resistance.value}")

        # Supply voltage
        self.supply_voltage = Parameter(value=1.0, unit="V", state=ParameterState.GIVEN)

        # Measurement device configuration
        self.meter_type = MeterType.VOLTMETER
        self.meter_measurement = Parameter(unit="V")

        # Analysis mode
        self.solve_mode = True  # True for Solve mode, False for List mode
    
    def get_resistor(self, name: str) -> ResistorState:
        """Get resistor by name (R1-R6)"""
        return self.resistors.get(name)
    
    def can_disable_resistor(self, name: str) -> bool:
        """Check if a resistor can be disabled"""
        return name in ['R1', 'R6']

    def set_resistor_enabled(self, name: str, enabled: bool):
        """Enable or disable a resistor (only R1 and R6 can be disabled)"""
        if name in self.resistors:
            # Only R1 and R6 can be disabled - R2, R3, R4, R5 form the core H-bridge
            if name in ['R1', 'R6']:
                self.resistors[name].enabled = enabled
            elif name in ['R2', 'R3', 'R4', 'R5']:
                # Core bridge resistors cannot be disabled
                self.resistors[name].enabled = True
    
    def set_meter_type(self, meter_type: MeterType):
        """Set the type of measurement device"""
        self.meter_type = meter_type
        if meter_type == MeterType.VOLTMETER:
            self.meter_measurement.unit = "V"
        else:
            self.meter_measurement.unit = "A"
    
    def get_enabled_resistors(self) -> Dict[str, ResistorState]:
        """Get resistors for analysis - includes disabled bridge components as short circuits"""
        enabled = {}
        for name, resistor in self.resistors.items():
            if resistor.enabled:
                enabled[name] = resistor
            elif name in ['R2', 'R3', 'R4', 'R5']:
                # Core bridge components are always included, even if disabled (as short circuits)
                enabled[name] = resistor
                print(f"DEBUG: Including disabled bridge component {name} as short circuit")
        return enabled
    
    def get_given_parameters(self) -> Dict[str, Dict[str, Parameter]]:
        """Get all parameters marked as given"""
        given = {}
        for name, resistor in self.resistors.items():
            if resistor.enabled:
                resistor_given = {}
                for param_name in ['resistance', 'voltage', 'current', 'power']:
                    param = resistor.get_parameter(param_name)
                    if param.is_given():
                        resistor_given[param_name] = param
                if resistor_given:
                    given[name] = resistor_given
        
        # Add supply voltage if given
        if self.supply_voltage.is_given():
            given['supply'] = {'voltage': self.supply_voltage}
        
        # Add meter measurement if given
        if self.meter_measurement.is_given():
            given['meter'] = {'measurement': self.meter_measurement}
        
        return given
    
    def get_unknown_parameters(self) -> Dict[str, Dict[str, Parameter]]:
        """Get all parameters marked as unknown"""
        unknown = {}
        for name, resistor in self.resistors.items():
            if resistor.enabled:
                resistor_unknown = {}
                for param_name in ['resistance', 'voltage', 'current', 'power']:
                    param = resistor.get_parameter(param_name)
                    if param.is_unknown():
                        resistor_unknown[param_name] = param
                if resistor_unknown:
                    unknown[name] = resistor_unknown
        
        # Add meter measurement if unknown
        if self.meter_measurement.is_unknown():
            unknown['meter'] = {'measurement': self.meter_measurement}
        
        return unknown
    
    def reset_all_states(self):
        """Reset all parameter states to inactive"""
        for resistor in self.resistors.values():
            for param_name in ['resistance', 'voltage', 'current', 'power']:
                resistor.get_parameter(param_name).state = ParameterState.INACTIVE
        self.meter_measurement.state = ParameterState.INACTIVE
    
    def validate_configuration(self) -> tuple[bool, str]:
        """Validate the current circuit configuration"""
        given = self.get_given_parameters()
        unknown = self.get_unknown_parameters()
        
        if not given:
            return False, "No given parameters specified"
        
        if not unknown and self.solve_mode:
            return False, "No unknown parameters specified for Solve mode"
        
        # Check if we have enough information to solve
        enabled_resistors = self.get_enabled_resistors()
        if len(enabled_resistors) == 0:
            return False, "No resistors enabled in circuit"
        
        return True, "Configuration valid"
