"""
Electrical Calculator for H-Bridge Analysis
Implements all electrical formulas and calculations for the circuit.
"""

import numpy as np
from typing import Dict, List, Tuple, Optional
from circuit_model import CircuitModel, Parameter, ParameterState, MeterType


class UnitConverter:
    """Handles unit conversions for electrical parameters"""
    
    RESISTANCE_UNITS = {
        'Ω': 1.0, 'ohm': 1.0,
        'kΩ': 1e3, 'kohm': 1e3,
        'MΩ': 1e6, 'Mohm': 1e6
    }
    
    VOLTAGE_UNITS = {
        'V': 1.0, 'volt': 1.0,
        'mV': 1e-3, 'millivolt': 1e-3,
        'kV': 1e3, 'kilovolt': 1e3
    }
    
    CURRENT_UNITS = {
        'A': 1.0, 'amp': 1.0,
        'mA': 1e-3, 'milliamp': 1e-3,
        'μA': 1e-6, 'microamp': 1e-6,
        'nA': 1e-9, 'nanoamp': 1e-9
    }
    
    POWER_UNITS = {
        'W': 1.0, 'watt': 1.0,
        'mW': 1e-3, 'milliwatt': 1e-3,
        'μW': 1e-6, 'microwatt': 1e-6
    }
    
    @classmethod
    def to_base_unit(cls, value: float, unit: str, param_type: str) -> float:
        """Convert value to base unit (Ω, V, A, W)"""
        unit_maps = {
            'resistance': cls.RESISTANCE_UNITS,
            'voltage': cls.VOLTAGE_UNITS,
            'current': cls.CURRENT_UNITS,
            'power': cls.POWER_UNITS
        }
        
        unit_map = unit_maps.get(param_type, {})
        multiplier = unit_map.get(unit, 1.0)
        return value * multiplier
    
    @classmethod
    def from_base_unit(cls, value: float, unit: str, param_type: str) -> float:
        """Convert value from base unit to specified unit"""
        unit_maps = {
            'resistance': cls.RESISTANCE_UNITS,
            'voltage': cls.VOLTAGE_UNITS,
            'current': cls.CURRENT_UNITS,
            'power': cls.POWER_UNITS
        }
        
        unit_map = unit_maps.get(param_type, {})
        multiplier = unit_map.get(unit, 1.0)
        return value / multiplier


class CalculationBlock:
    """Represents a single electrical formula/relationship"""

    def __init__(self, name: str, formula_func, required_params: list, calculates: str):
        self.name = name
        self.formula_func = formula_func
        self.required_params = required_params  # Parameters needed for calculation
        self.calculates = calculates  # Parameter this block calculates

    def can_calculate(self, known_params: dict) -> bool:
        """Check if this block can calculate with given parameters"""
        return all(param in known_params for param in self.required_params)

    def calculate(self, known_params: dict) -> float:
        """Perform the calculation"""
        return self.formula_func(known_params)


class ElectricalCalculator:
    """Enhanced calculator with iterative solving and multiple calculation paths"""

    def __init__(self):
        self.converter = UnitConverter()
        self.calculation_blocks = self._create_calculation_blocks()
        self.calculation_history = []

    def _create_calculation_blocks(self) -> list:
        """Create all electrical formula blocks"""
        blocks = []

        # Ohm's Law variations
        blocks.append(CalculationBlock(
            "Ohm's Law V=IR",
            lambda p: p['current'] * p['resistance'],
            ['current', 'resistance'], 'voltage'
        ))
        blocks.append(CalculationBlock(
            "Ohm's Law I=V/R",
            lambda p: p['voltage'] / p['resistance'] if p['resistance'] != 0 else 0,
            ['voltage', 'resistance'], 'current'
        ))
        blocks.append(CalculationBlock(
            "Ohm's Law R=V/I",
            lambda p: p['voltage'] / p['current'] if p['current'] != 0 else float('inf'),
            ['voltage', 'current'], 'resistance'
        ))

        # Power formulas
        blocks.append(CalculationBlock(
            "Power P=VI",
            lambda p: p['voltage'] * p['current'],
            ['voltage', 'current'], 'power'
        ))
        blocks.append(CalculationBlock(
            "Power P=V²/R",
            lambda p: p['voltage']**2 / p['resistance'] if p['resistance'] != 0 else 0,
            ['voltage', 'resistance'], 'power'
        ))
        blocks.append(CalculationBlock(
            "Power P=I²R",
            lambda p: p['current']**2 * p['resistance'],
            ['current', 'resistance'], 'power'
        ))
        blocks.append(CalculationBlock(
            "Current I=P/V",
            lambda p: p['power'] / p['voltage'] if p['voltage'] != 0 else 0,
            ['power', 'voltage'], 'current'
        ))
        blocks.append(CalculationBlock(
            "Voltage V=P/I",
            lambda p: p['power'] / p['current'] if p['current'] != 0 else 0,
            ['power', 'current'], 'voltage'
        ))
        blocks.append(CalculationBlock(
            "Resistance R=P/I²",
            lambda p: p['power'] / p['current']**2 if p['current'] != 0 else float('inf'),
            ['power', 'current'], 'resistance'
        ))
        blocks.append(CalculationBlock(
            "Resistance R=V²/P",
            lambda p: p['voltage']**2 / p['power'] if p['power'] != 0 else float('inf'),
            ['voltage', 'power'], 'resistance'
        ))

        return blocks

    def iterative_solve(self, known_params: Dict[str, float], max_iterations: int = 10) -> Dict[str, float]:
        """
        Iterative solver that applies all possible calculation blocks until no more progress
        """
        self.calculation_history = []
        results = known_params.copy()

        for iteration in range(max_iterations):
            initial_count = len(results)

            # Try each calculation block
            for block in self.calculation_blocks:
                if block.can_calculate(results) and block.calculates not in results:
                    try:
                        calculated_value = block.calculate(results)
                        if calculated_value is not None and not (isinstance(calculated_value, float) and
                                                               (calculated_value == float('inf') or calculated_value != calculated_value)):
                            results[block.calculates] = calculated_value
                            self.calculation_history.append({
                                'iteration': iteration + 1,
                                'block': block.name,
                                'calculated': block.calculates,
                                'value': calculated_value,
                                'used_params': {p: results[p] for p in block.required_params}
                            })
                    except (ZeroDivisionError, ValueError, TypeError):
                        continue

            # Check if we made progress
            if len(results) == initial_count:
                break  # No new calculations possible

        return results

    def solve_all_parameters(self, known_params: Dict[str, float]) -> Dict[str, float]:
        """
        Enhanced parameter solver using iterative calculation blocks
        """
        if not known_params:
            return {}

        # Use iterative solver for comprehensive calculation
        return self.iterative_solve(known_params)

    def solve_network_analysis(self, circuit_config: Dict) -> Dict[str, float]:
        """
        Advanced network analysis using Kirchhoff's laws and network theorems
        """
        results = {}

        # Extract circuit configuration
        resistances = circuit_config.get('resistances', {})
        voltages = circuit_config.get('voltages', {})
        currents = circuit_config.get('currents', {})

        # Apply Kirchhoff's Current Law (KCL) at nodes
        # Apply Kirchhoff's Voltage Law (KVL) around loops
        # Use nodal analysis and mesh analysis

        # This would implement sophisticated circuit analysis
        # For now, return basic analysis
        return results
    
    def parallel_resistance(self, resistances: List[float]) -> float:
        """Calculate equivalent resistance of parallel resistors"""
        if not resistances:
            return float('inf')
        
        # Remove zero and infinite resistances
        valid_resistances = [r for r in resistances if r > 0 and r != float('inf')]
        
        if not valid_resistances:
            return float('inf')
        
        # 1/R_eq = 1/R1 + 1/R2 + ...
        reciprocal_sum = sum(1/r for r in valid_resistances)
        return 1/reciprocal_sum if reciprocal_sum != 0 else float('inf')
    
    def series_resistance(self, resistances: List[float]) -> float:
        """Calculate equivalent resistance of series resistors"""
        return sum(r for r in resistances if r != float('inf'))
    
    def voltage_divider(self, v_in: float, r1: float, r2: float) -> float:
        """Calculate voltage divider output: V_out = V_in * R2 / (R1 + R2)"""
        total_r = r1 + r2
        if total_r == 0:
            return 0
        return v_in * r2 / total_r
    
    def current_divider(self, i_in: float, r1: float, r2: float) -> Tuple[float, float]:
        """Calculate current division: I1 = I_in * R2/(R1+R2), I2 = I_in * R1/(R1+R2)"""
        total_r = r1 + r2
        if total_r == 0:
            return 0, 0
        i1 = i_in * r2 / total_r
        i2 = i_in * r1 / total_r
        return i1, i2
    
    def wheatstone_bridge_analysis(self, r1: float, r2: float, r3: float, r4: float, 
                                 v_supply: float) -> Dict[str, float]:
        """
        Analyze Wheatstone bridge configuration
        Returns bridge voltage and currents
        """
        # Calculate bridge voltage (voltage between middle points)
        # V_bridge = V_supply * (R3/(R1+R3) - R4/(R2+R4))
        
        if r1 + r3 == 0 or r2 + r4 == 0:
            return {'bridge_voltage': 0, 'bridge_current': 0}
        
        v_left = v_supply * r3 / (r1 + r3)
        v_right = v_supply * r4 / (r2 + r4)
        v_bridge = v_left - v_right
        
        # Calculate currents through each branch
        i_left = v_supply / (r1 + r3) if (r1 + r3) != 0 else 0
        i_right = v_supply / (r2 + r4) if (r2 + r4) != 0 else 0
        
        return {
            'bridge_voltage': v_bridge,
            'v_left': v_left,
            'v_right': v_right,
            'i_left': i_left,
            'i_right': i_right,
            'i_r1': i_left,
            'i_r2': i_right,
            'i_r3': i_left,
            'i_r4': i_right
        }
    
    def solve_circuit(self, model: CircuitModel) -> Dict[str, Dict[str, float]]:
        """
        Enhanced H-bridge circuit analysis with iterative solving and proper unit handling
        """
        results = {}

        # Get enabled resistors and extract all parameters
        enabled = model.get_enabled_resistors()

        # Extract all known parameters with proper unit conversion
        all_known_params = {}
        resistances = {}

        for name, resistor in enabled.items():
            # Convert all known parameters to base units for calculation
            known = {}

            if resistor.resistance.is_given():
                known['resistance'] = self.converter.to_base_unit(
                    resistor.resistance.value, resistor.resistance.unit, 'resistance')
                resistances[name] = known['resistance']
            if resistor.voltage.is_given():
                known['voltage'] = self.converter.to_base_unit(
                    resistor.voltage.value, resistor.voltage.unit, 'voltage')
            if resistor.current.is_given():
                known['current'] = self.converter.to_base_unit(
                    resistor.current.value, resistor.current.unit, 'current')
            if resistor.power.is_given():
                known['power'] = self.converter.to_base_unit(
                    resistor.power.value, resistor.power.unit, 'power')

            all_known_params[name] = known

            # Set default resistance if not given
            if name not in resistances:
                resistances[name] = 1000.0  # Default 1kΩ

        # Get supply voltage in base units
        v_supply = self.converter.to_base_unit(
            model.supply_voltage.value,
            model.supply_voltage.unit,
            'voltage'
        )

        # Step 1: Use iterative solver for each component
        for name, resistor in enabled.items():
            known = all_known_params[name]

            # Use enhanced iterative solver
            calculated = self.iterative_solve(known)

            # Store results with proper units
            component_results = {}
            for param_name, value in calculated.items():
                component_results[param_name] = value

            results[name] = component_results

        # Step 2: Apply H-bridge circuit analysis with multiple calculation paths
        self._apply_hbridge_analysis(results, resistances, v_supply, enabled, model)

        # Step 3: Convert results back to display units and ensure all have units
        self._format_results_with_units(results, enabled)

        return results

    def _apply_hbridge_analysis(self, results: Dict, resistances: Dict, v_supply: float,
                               enabled: Dict, model: CircuitModel):
        """Apply H-bridge specific calculations with multiple paths"""

        # Calculate total circuit current (through R1)
        total_resistance = self._calculate_total_resistance(enabled, resistances)
        total_current = v_supply / total_resistance if total_resistance != 0 else 0

        # Apply circuit-level calculations
        if 'R1' in enabled:
            r1_resistance = resistances.get('R1', 1000)
            if 'R1' not in results:
                results['R1'] = {}
            results['R1'].update({
                'resistance': r1_resistance,
                'current': total_current,
                'voltage': total_current * r1_resistance,
                'power': total_current**2 * r1_resistance
            })

        # H-bridge analysis for R2-R5
        if all(name in enabled for name in ['R2', 'R3', 'R4', 'R5']):
            bridge_results = self.wheatstone_bridge_analysis(
                resistances.get('R2', 1000),
                resistances.get('R4', 1000),
                resistances.get('R3', 1000),
                resistances.get('R5', 1000),
                v_supply
            )

            # Apply bridge results
            bridge_components = ['R2', 'R3', 'R4', 'R5']
            for comp in bridge_components:
                if comp not in results:
                    results[comp] = {}

                # Calculate voltage across each bridge resistor
                if comp == 'R2':
                    results[comp].update({
                        'resistance': resistances.get(comp, 1000),
                        'current': bridge_results.get('i_r2', 0),
                        'voltage': bridge_results.get('i_r2', 0) * resistances.get(comp, 1000),
                        'power': bridge_results.get('i_r2', 0)**2 * resistances.get(comp, 1000)
                    })
                # Similar for R3, R4, R5...

        # R6 analysis (output voltage calculations)
        if 'R6' in enabled:
            if 'R6' not in results:
                results['R6'] = {}

            # Multiple ways to calculate U2 (output voltage)
            u2_voltage = self._calculate_output_voltage_multiple_paths(results, resistances, v_supply)
            r6_resistance = resistances.get('R6', 1000)
            r6_current = u2_voltage / r6_resistance if r6_resistance != 0 else 0

            results['R6'].update({
                'resistance': r6_resistance,
                'voltage': u2_voltage,
                'current': r6_current,
                'power': r6_current**2 * r6_resistance
            })

    def _calculate_output_voltage_multiple_paths(self, results: Dict, resistances: Dict, v_supply: float) -> float:
        """Calculate U2 using multiple electrical relationships"""

        # Path 1: U2 = Voltage across R6
        if 'R6' in results and 'voltage' in results['R6']:
            return results['R6']['voltage']

        # Path 2: U2 = Voltage across (R5 + R4) - simplified
        # Path 3: U2 = U1 - UR1 (supply voltage minus R1 voltage drop)
        if 'R1' in results and 'voltage' in results['R1']:
            return v_supply - results['R1']['voltage']

        # Default calculation
        return v_supply * 0.1  # Simplified default

    def _format_results_with_units(self, results: Dict, enabled: Dict):
        """Ensure all results have proper units displayed"""
        unit_map = {
            'resistance': 'Ω',
            'voltage': 'V',
            'current': 'A',
            'power': 'W'
        }

        for name, component_results in results.items():
            if name in enabled:
                resistor = enabled[name]
                for param_name, value in component_results.items():
                    if param_name in unit_map:
                        # Get the parameter object to preserve user's unit preference
                        param_obj = getattr(resistor, param_name, None)
                        if param_obj and param_obj.unit:
                            # Convert from base unit to user's preferred unit
                            display_value = self.converter.from_base_unit(
                                value, param_obj.unit, param_name)
                            component_results[param_name] = display_value
                        else:
                            # Use default unit
                            component_results[param_name] = value

        return results

    def analyze_hbridge_comprehensive(self, resistances: Dict[str, float],
                                    v_supply: float, known_params: Dict,
                                    model: CircuitModel) -> Dict[str, Dict[str, float]]:
        """Comprehensive H-bridge analysis using multiple methods"""
        results = {}

        # Get bridge resistances
        r2 = resistances.get('R2', 1000)
        r3 = resistances.get('R3', 1000)
        r4 = resistances.get('R4', 1000)
        r5 = resistances.get('R5', 1000)

        # Wheatstone bridge analysis
        bridge_results = self.wheatstone_bridge_analysis(r2, r4, r3, r5, v_supply)

        # Calculate individual resistor parameters
        bridge_components = ['R2', 'R3', 'R4', 'R5']
        bridge_currents = {
            'R2': bridge_results.get('i_r2', 0),
            'R3': bridge_results.get('i_r3', 0),
            'R4': bridge_results.get('i_r4', 0),
            'R5': bridge_results.get('i_r5', 0)
        }

        for name in bridge_components:
            if name in resistances:
                r_val = resistances[name]
                i_val = bridge_currents[name]
                v_val = i_val * r_val
                p_val = v_val * i_val

                results[name] = {
                    'resistance': r_val,
                    'current': i_val,
                    'voltage': v_val,
                    'power': p_val
                }

        return results

    def analyze_series_resistor(self, resistances: Dict[str, float],
                              v_supply: float, known_params: Dict,
                              enabled: Dict) -> Dict[str, float]:
        """Analyze series resistor R1"""
        # Calculate total circuit resistance
        total_r = self._calculate_total_resistance(enabled, resistances)

        # Total circuit current
        total_current = v_supply / total_r if total_r != 0 else 0

        # R1 parameters
        r1_val = resistances.get('R1', 1000)
        v1_val = total_current * r1_val
        p1_val = v1_val * total_current

        return {
            'resistance': r1_val,
            'current': total_current,
            'voltage': v1_val,
            'power': p1_val
        }

    def analyze_parallel_resistor(self, resistances: Dict[str, float],
                                v_supply: float, known_params: Dict,
                                enabled: Dict, circuit_results: Dict) -> Dict[str, float]:
        """Analyze parallel resistor R6"""
        # Calculate output voltage (after R1 and bridge)
        output_voltage = v_supply

        # Subtract R1 voltage drop if present
        if 'R1' in circuit_results:
            output_voltage -= circuit_results['R1'].get('voltage', 0)

        # R6 parameters
        r6_val = resistances.get('R6', 1000)
        i6_val = output_voltage / r6_val if r6_val != 0 else 0
        p6_val = output_voltage * i6_val

        return {
            'resistance': r6_val,
            'current': i6_val,
            'voltage': output_voltage,
            'power': p6_val
        }

    def calculate_meter_reading(self, resistances: Dict[str, float],
                              v_supply: float, meter_type: MeterType,
                              circuit_results: Dict) -> float:
        """Calculate measurement device reading"""
        if meter_type == MeterType.VOLTMETER:
            # Voltage across the bridge
            bridge_results = self.wheatstone_bridge_analysis(
                resistances.get('R2', 1000),
                resistances.get('R4', 1000),
                resistances.get('R3', 1000),
                resistances.get('R5', 1000),
                v_supply
            )
            return bridge_results.get('bridge_voltage', 0)
        else:
            # Current through the bridge (simplified)
            bridge_voltage = self.calculate_meter_reading(
                resistances, v_supply, MeterType.VOLTMETER, circuit_results
            )
            # Assume 1kΩ meter resistance
            return bridge_voltage / 1000.0
    
    def _calculate_total_resistance(self, enabled: Dict, resistances: Dict[str, float]) -> float:
        """Calculate total circuit resistance"""
        # This is a simplified calculation - in practice, this would need
        # more sophisticated network analysis for complex configurations
        total = 0
        
        # Add series resistance R1
        if 'R1' in enabled:
            total += resistances['R1']
        
        # Calculate bridge equivalent resistance
        bridge_resistors = ['R2', 'R3', 'R4', 'R5']
        bridge_enabled = [name for name in bridge_resistors if name in enabled]
        
        if len(bridge_enabled) >= 2:
            # Simplified bridge resistance calculation
            bridge_r = sum(resistances[name] for name in bridge_enabled) / 2
            total += bridge_r
        
        # Add parallel resistance R6
        if 'R6' in enabled and total > 0:
            r6 = resistances['R6']
            total = self.parallel_resistance([total, r6])
        
        return total
