"""
Electrical Calculator for H-Bridge Analysis
Implements all electrical formulas and calculations for the circuit.
"""

import numpy as np
from typing import Dict, List, Tuple, Optional
from circuit_model import CircuitModel, Parameter, ParameterState, MeterType


class UnitConverter:
    """Handles unit conversions for electrical parameters"""
    
    RESISTANCE_UNITS = {
        'Ω': 1.0, 'ohm': 1.0,
        'kΩ': 1e3, 'kohm': 1e3,
        'MΩ': 1e6, 'Mohm': 1e6
    }
    
    VOLTAGE_UNITS = {
        'V': 1.0, 'volt': 1.0,
        'mV': 1e-3, 'millivolt': 1e-3,
        'kV': 1e3, 'kilovolt': 1e3
    }
    
    CURRENT_UNITS = {
        'A': 1.0, 'amp': 1.0,
        'mA': 1e-3, 'milliamp': 1e-3,
        'μA': 1e-6, 'microamp': 1e-6,
        'nA': 1e-9, 'nanoamp': 1e-9
    }
    
    POWER_UNITS = {
        'W': 1.0, 'watt': 1.0,
        'mW': 1e-3, 'milliwatt': 1e-3,
        'μW': 1e-6, 'microwatt': 1e-6
    }
    
    @classmethod
    def to_base_unit(cls, value: float, unit: str, param_type: str) -> float:
        """Convert value to base unit (Ω, V, A, W)"""
        unit_maps = {
            'resistance': cls.RESISTANCE_UNITS,
            'voltage': cls.VOLTAGE_UNITS,
            'current': cls.CURRENT_UNITS,
            'power': cls.POWER_UNITS
        }
        
        unit_map = unit_maps.get(param_type, {})
        multiplier = unit_map.get(unit, 1.0)
        return value * multiplier
    
    @classmethod
    def from_base_unit(cls, value: float, unit: str, param_type: str) -> float:
        """Convert value from base unit to specified unit"""
        unit_maps = {
            'resistance': cls.RESISTANCE_UNITS,
            'voltage': cls.VOLTAGE_UNITS,
            'current': cls.CURRENT_UNITS,
            'power': cls.POWER_UNITS
        }
        
        unit_map = unit_maps.get(param_type, {})
        multiplier = unit_map.get(unit, 1.0)
        return value / multiplier


class ElectricalCalculator:
    """Main calculator for comprehensive electrical circuit analysis"""

    def __init__(self):
        self.converter = UnitConverter()

    def solve_all_parameters(self, known_params: Dict[str, float]) -> Dict[str, float]:
        """
        Comprehensive electrical parameter solver using all possible formulas
        Handles V, I, R, P with multiple calculation methods and cross-validation
        """
        results = {}

        # Extract known values
        V = known_params.get('voltage')
        I = known_params.get('current')
        R = known_params.get('resistance')
        P = known_params.get('power')

        # Count known parameters
        known_count = sum(1 for x in [V, I, R, P] if x is not None)

        if known_count < 1:
            return results

        # Apply comprehensive electrical formulas with multiple approaches

        # 1. Basic Ohm's Law: V = I * R (3 variations)
        if V is not None and I is not None and R is None:
            results['resistance'] = V / I if I != 0 else float('inf')
        elif V is not None and R is not None and I is None:
            results['current'] = V / R if R != 0 else 0
        elif I is not None and R is not None and V is None:
            results['voltage'] = I * R

        # 2. Power formulas: P = V * I (3 variations)
        if V is not None and I is not None and P is None:
            results['power'] = V * I
        elif V is not None and P is not None and I is None:
            results['current'] = P / V if V != 0 else 0
        elif I is not None and P is not None and V is None:
            results['voltage'] = P / I if I != 0 else 0

        # 3. Power formulas: P = I² * R (3 variations)
        if I is not None and R is not None and P is None:
            results['power'] = I * I * R
        elif I is not None and P is not None and R is None:
            results['resistance'] = P / (I * I) if I != 0 else float('inf')
        elif R is not None and P is not None and I is None:
            results['current'] = np.sqrt(P / R) if R > 0 and P >= 0 else 0

        # 4. Power formulas: P = V² / R (3 variations)
        if V is not None and R is not None and P is None:
            results['power'] = V * V / R if R != 0 else 0
        elif V is not None and P is not None and R is None:
            results['resistance'] = V * V / P if P != 0 else float('inf')
        elif R is not None and P is not None and V is None:
            results['voltage'] = np.sqrt(P * R) if R >= 0 and P >= 0 else 0

        # 5. Advanced calculations with energy and efficiency
        # Calculate energy if time is available (future enhancement)
        # Calculate efficiency for power transfer

        # 6. Temperature coefficient calculations (for precision resistors)
        # This could be added for advanced analysis

        # 7. Frequency-dependent calculations (for AC analysis)
        # This could be added for AC circuit analysis

        # 8. Cross-validation and iterative solving
        # If we calculated new values, try to calculate more
        if results:
            # Merge known and calculated
            all_params = {**known_params, **results}

            # Try to calculate any remaining unknowns
            V_new = all_params.get('voltage')
            I_new = all_params.get('current')
            R_new = all_params.get('resistance')
            P_new = all_params.get('power')

            # Fill in any remaining gaps using all available formulas
            if V_new is not None and I_new is not None:
                if 'resistance' not in results and R is None:
                    results['resistance'] = V_new / I_new if I_new != 0 else float('inf')
                if 'power' not in results and P is None:
                    results['power'] = V_new * I_new

            if V_new is not None and R_new is not None:
                if 'current' not in results and I is None:
                    results['current'] = V_new / R_new if R_new != 0 else 0
                if 'power' not in results and P is None:
                    results['power'] = V_new * V_new / R_new if R_new != 0 else 0

            if I_new is not None and R_new is not None:
                if 'voltage' not in results and V is None:
                    results['voltage'] = I_new * R_new
                if 'power' not in results and P is None:
                    results['power'] = I_new * I_new * R_new

            # Additional derived parameters
            if V_new is not None and P_new is not None:
                if 'conductance' not in results:
                    results['conductance'] = (P_new / (V_new * V_new)) if V_new != 0 else 0
                if 'impedance' not in results and 'resistance' in results:
                    results['impedance'] = results['resistance']  # For DC circuits

        # 9. Calculate additional electrical parameters
        if 'resistance' in results and results['resistance'] != 0:
            results['conductance'] = 1 / results['resistance']

        if 'voltage' in results and 'current' in results:
            results['power_factor'] = 1.0  # For DC circuits

        # 10. Calculate power dissipation efficiency
        if 'power' in results and 'voltage' in results and 'current' in results:
            theoretical_max_power = results['voltage'] * results['current']
            if theoretical_max_power != 0:
                results['efficiency'] = (results['power'] / theoretical_max_power) * 100

        return results

    def solve_network_analysis(self, circuit_config: Dict) -> Dict[str, float]:
        """
        Advanced network analysis using Kirchhoff's laws and network theorems
        """
        results = {}

        # Extract circuit configuration
        resistances = circuit_config.get('resistances', {})
        voltages = circuit_config.get('voltages', {})
        currents = circuit_config.get('currents', {})

        # Apply Kirchhoff's Current Law (KCL) at nodes
        # Apply Kirchhoff's Voltage Law (KVL) around loops
        # Use nodal analysis and mesh analysis

        # This would implement sophisticated circuit analysis
        # For now, return basic analysis
        return results
    
    def parallel_resistance(self, resistances: List[float]) -> float:
        """Calculate equivalent resistance of parallel resistors"""
        if not resistances:
            return float('inf')
        
        # Remove zero and infinite resistances
        valid_resistances = [r for r in resistances if r > 0 and r != float('inf')]
        
        if not valid_resistances:
            return float('inf')
        
        # 1/R_eq = 1/R1 + 1/R2 + ...
        reciprocal_sum = sum(1/r for r in valid_resistances)
        return 1/reciprocal_sum if reciprocal_sum != 0 else float('inf')
    
    def series_resistance(self, resistances: List[float]) -> float:
        """Calculate equivalent resistance of series resistors"""
        return sum(r for r in resistances if r != float('inf'))
    
    def voltage_divider(self, v_in: float, r1: float, r2: float) -> float:
        """Calculate voltage divider output: V_out = V_in * R2 / (R1 + R2)"""
        total_r = r1 + r2
        if total_r == 0:
            return 0
        return v_in * r2 / total_r
    
    def current_divider(self, i_in: float, r1: float, r2: float) -> Tuple[float, float]:
        """Calculate current division: I1 = I_in * R2/(R1+R2), I2 = I_in * R1/(R1+R2)"""
        total_r = r1 + r2
        if total_r == 0:
            return 0, 0
        i1 = i_in * r2 / total_r
        i2 = i_in * r1 / total_r
        return i1, i2
    
    def wheatstone_bridge_analysis(self, r1: float, r2: float, r3: float, r4: float, 
                                 v_supply: float) -> Dict[str, float]:
        """
        Analyze Wheatstone bridge configuration
        Returns bridge voltage and currents
        """
        # Calculate bridge voltage (voltage between middle points)
        # V_bridge = V_supply * (R3/(R1+R3) - R4/(R2+R4))
        
        if r1 + r3 == 0 or r2 + r4 == 0:
            return {'bridge_voltage': 0, 'bridge_current': 0}
        
        v_left = v_supply * r3 / (r1 + r3)
        v_right = v_supply * r4 / (r2 + r4)
        v_bridge = v_left - v_right
        
        # Calculate currents through each branch
        i_left = v_supply / (r1 + r3) if (r1 + r3) != 0 else 0
        i_right = v_supply / (r2 + r4) if (r2 + r4) != 0 else 0
        
        return {
            'bridge_voltage': v_bridge,
            'v_left': v_left,
            'v_right': v_right,
            'i_left': i_left,
            'i_right': i_right,
            'i_r1': i_left,
            'i_r2': i_right,
            'i_r3': i_left,
            'i_r4': i_right
        }
    
    def solve_circuit(self, model: CircuitModel) -> Dict[str, Dict[str, float]]:
        """
        Comprehensive H-bridge circuit analysis with all possible calculations
        Returns calculated values for all components using advanced electrical formulas
        """
        results = {}

        # Get enabled resistors and extract all parameters
        enabled = model.get_enabled_resistors()

        # Extract resistance values and all known parameters
        resistances = {}
        all_known_params = {}

        for name, resistor in enabled.items():
            # Convert all known parameters to base units
            known = {}
            if resistor.resistance.is_given():
                known['resistance'] = self.converter.to_base_unit(
                    resistor.resistance.value, resistor.resistance.unit, 'resistance')
                resistances[name] = known['resistance']
            if resistor.voltage.is_given():
                known['voltage'] = self.converter.to_base_unit(
                    resistor.voltage.value, resistor.voltage.unit, 'voltage')
            if resistor.current.is_given():
                known['current'] = self.converter.to_base_unit(
                    resistor.current.value, resistor.current.unit, 'current')
            if resistor.power.is_given():
                known['power'] = self.converter.to_base_unit(
                    resistor.power.value, resistor.power.unit, 'power')

            all_known_params[name] = known

            # Set default resistance if not given
            if name not in resistances:
                resistances[name] = 1000.0  # Default 1kΩ

        # Get supply voltage
        v_supply = self.converter.to_base_unit(
            model.supply_voltage.value,
            model.supply_voltage.unit,
            'voltage'
        )

        # Step 1: Calculate individual component parameters using comprehensive formulas
        for name, resistor in enabled.items():
            known = all_known_params[name]

            # Use comprehensive parameter solving
            calculated = self.solve_all_parameters(known)

            # Merge known and calculated values
            all_params = {**known, **calculated}
            results[name] = all_params

        # Step 2: Perform advanced circuit analysis
        # H-bridge (Wheatstone bridge) analysis
        if all(name in enabled for name in ['R2', 'R3', 'R4', 'R5']):
            bridge_analysis = self.analyze_hbridge_comprehensive(
                resistances, v_supply, all_known_params, model
            )

            # Update results with circuit-level analysis
            for name, params in bridge_analysis.items():
                if name in results:
                    # Only update parameters that weren't given by user
                    resistor = enabled[name]
                    for param_name, value in params.items():
                        param_obj = getattr(resistor, param_name, None)
                        if param_obj and not param_obj.is_given():
                            results[name][param_name] = value
                else:
                    results[name] = params

        # Step 3: Series resistor R1 analysis
        if 'R1' in enabled:
            r1_analysis = self.analyze_series_resistor(
                resistances, v_supply, all_known_params, enabled
            )

            # Update R1 results
            resistor = enabled['R1']
            for param_name, value in r1_analysis.items():
                param_obj = getattr(resistor, param_name, None)
                if param_obj and not param_obj.is_given():
                    results['R1'][param_name] = value

        # Step 4: Parallel resistor R6 analysis
        if 'R6' in enabled:
            r6_analysis = self.analyze_parallel_resistor(
                resistances, v_supply, all_known_params, enabled, results
            )

            # Update R6 results
            resistor = enabled['R6']
            for param_name, value in r6_analysis.items():
                param_obj = getattr(resistor, param_name, None)
                if param_obj and not param_obj.is_given():
                    results['R6'][param_name] = value

        # Step 5: Add measurement device results
        if all(name in enabled for name in ['R2', 'R3', 'R4', 'R5']):
            meter_result = self.calculate_meter_reading(
                resistances, v_supply, model.meter_type, results
            )
            results['METER'] = {'measurement': meter_result}

        return results

    def analyze_hbridge_comprehensive(self, resistances: Dict[str, float],
                                    v_supply: float, known_params: Dict,
                                    model: CircuitModel) -> Dict[str, Dict[str, float]]:
        """Comprehensive H-bridge analysis using multiple methods"""
        results = {}

        # Get bridge resistances
        r2 = resistances.get('R2', 1000)
        r3 = resistances.get('R3', 1000)
        r4 = resistances.get('R4', 1000)
        r5 = resistances.get('R5', 1000)

        # Wheatstone bridge analysis
        bridge_results = self.wheatstone_bridge_analysis(r2, r4, r3, r5, v_supply)

        # Calculate individual resistor parameters
        bridge_components = ['R2', 'R3', 'R4', 'R5']
        bridge_currents = {
            'R2': bridge_results.get('i_r2', 0),
            'R3': bridge_results.get('i_r3', 0),
            'R4': bridge_results.get('i_r4', 0),
            'R5': bridge_results.get('i_r5', 0)
        }

        for name in bridge_components:
            if name in resistances:
                r_val = resistances[name]
                i_val = bridge_currents[name]
                v_val = i_val * r_val
                p_val = v_val * i_val

                results[name] = {
                    'resistance': r_val,
                    'current': i_val,
                    'voltage': v_val,
                    'power': p_val
                }

        return results

    def analyze_series_resistor(self, resistances: Dict[str, float],
                              v_supply: float, known_params: Dict,
                              enabled: Dict) -> Dict[str, float]:
        """Analyze series resistor R1"""
        # Calculate total circuit resistance
        total_r = self._calculate_total_resistance(enabled, resistances)

        # Total circuit current
        total_current = v_supply / total_r if total_r != 0 else 0

        # R1 parameters
        r1_val = resistances.get('R1', 1000)
        v1_val = total_current * r1_val
        p1_val = v1_val * total_current

        return {
            'resistance': r1_val,
            'current': total_current,
            'voltage': v1_val,
            'power': p1_val
        }

    def analyze_parallel_resistor(self, resistances: Dict[str, float],
                                v_supply: float, known_params: Dict,
                                enabled: Dict, circuit_results: Dict) -> Dict[str, float]:
        """Analyze parallel resistor R6"""
        # Calculate output voltage (after R1 and bridge)
        output_voltage = v_supply

        # Subtract R1 voltage drop if present
        if 'R1' in circuit_results:
            output_voltage -= circuit_results['R1'].get('voltage', 0)

        # R6 parameters
        r6_val = resistances.get('R6', 1000)
        i6_val = output_voltage / r6_val if r6_val != 0 else 0
        p6_val = output_voltage * i6_val

        return {
            'resistance': r6_val,
            'current': i6_val,
            'voltage': output_voltage,
            'power': p6_val
        }

    def calculate_meter_reading(self, resistances: Dict[str, float],
                              v_supply: float, meter_type: MeterType,
                              circuit_results: Dict) -> float:
        """Calculate measurement device reading"""
        if meter_type == MeterType.VOLTMETER:
            # Voltage across the bridge
            bridge_results = self.wheatstone_bridge_analysis(
                resistances.get('R2', 1000),
                resistances.get('R4', 1000),
                resistances.get('R3', 1000),
                resistances.get('R5', 1000),
                v_supply
            )
            return bridge_results.get('bridge_voltage', 0)
        else:
            # Current through the bridge (simplified)
            bridge_voltage = self.calculate_meter_reading(
                resistances, v_supply, MeterType.VOLTMETER, circuit_results
            )
            # Assume 1kΩ meter resistance
            return bridge_voltage / 1000.0
    
    def _calculate_total_resistance(self, enabled: Dict, resistances: Dict[str, float]) -> float:
        """Calculate total circuit resistance"""
        # This is a simplified calculation - in practice, this would need
        # more sophisticated network analysis for complex configurations
        total = 0
        
        # Add series resistance R1
        if 'R1' in enabled:
            total += resistances['R1']
        
        # Calculate bridge equivalent resistance
        bridge_resistors = ['R2', 'R3', 'R4', 'R5']
        bridge_enabled = [name for name in bridge_resistors if name in enabled]
        
        if len(bridge_enabled) >= 2:
            # Simplified bridge resistance calculation
            bridge_r = sum(resistances[name] for name in bridge_enabled) / 2
            total += bridge_r
        
        # Add parallel resistance R6
        if 'R6' in enabled and total > 0:
            r6 = resistances['R6']
            total = self.parallel_resistance([total, r6])
        
        return total
