"""
Electrical Calculator for H-Bridge Analysis
Implements all electrical formulas and calculations for the circuit.
"""

import numpy as np
from typing import Dict, List, Tuple, Optional
from circuit_model import CircuitModel, Parameter, ParameterState, MeterType


class UnitConverter:
    """Handles unit conversions for electrical parameters"""
    
    RESISTANCE_UNITS = {
        'Ω': 1.0, 'ohm': 1.0,
        'kΩ': 1e3, 'kohm': 1e3,
        'MΩ': 1e6, 'Mohm': 1e6
    }
    
    VOLTAGE_UNITS = {
        'V': 1.0, 'volt': 1.0,
        'mV': 1e-3, 'millivolt': 1e-3,
        'kV': 1e3, 'kilovolt': 1e3
    }
    
    CURRENT_UNITS = {
        'A': 1.0, 'amp': 1.0,
        'mA': 1e-3, 'milliamp': 1e-3,
        'μA': 1e-6, 'microamp': 1e-6,
        'nA': 1e-9, 'nanoamp': 1e-9
    }
    
    POWER_UNITS = {
        'W': 1.0, 'watt': 1.0,
        'mW': 1e-3, 'milliwatt': 1e-3,
        'μW': 1e-6, 'microwatt': 1e-6
    }
    
    @classmethod
    def to_base_unit(cls, value: float, unit: str, param_type: str) -> float:
        """Convert value to base unit (Ω, V, A, W)"""
        unit_maps = {
            'resistance': cls.RESISTANCE_UNITS,
            'voltage': cls.VOLTAGE_UNITS,
            'current': cls.CURRENT_UNITS,
            'power': cls.POWER_UNITS
        }
        
        unit_map = unit_maps.get(param_type, {})
        multiplier = unit_map.get(unit, 1.0)
        return value * multiplier
    
    @classmethod
    def from_base_unit(cls, value: float, unit: str, param_type: str) -> float:
        """Convert value from base unit to specified unit"""
        unit_maps = {
            'resistance': cls.RESISTANCE_UNITS,
            'voltage': cls.VOLTAGE_UNITS,
            'current': cls.CURRENT_UNITS,
            'power': cls.POWER_UNITS
        }
        
        unit_map = unit_maps.get(param_type, {})
        multiplier = unit_map.get(unit, 1.0)
        return value / multiplier


class ElectricalCalculator:
    """Main calculator for electrical circuit analysis"""
    
    def __init__(self):
        self.converter = UnitConverter()
    
    def ohms_law_calculate(self, known_params: Dict[str, float]) -> Dict[str, float]:
        """
        Calculate unknown parameters using Ohm's law
        V = I * R, P = V * I = I²R = V²/R
        """
        results = {}
        
        # Extract known values
        V = known_params.get('voltage')
        I = known_params.get('current')
        R = known_params.get('resistance')
        P = known_params.get('power')
        
        # Calculate missing values
        if V is not None and I is not None:
            if R is None:
                results['resistance'] = V / I if I != 0 else float('inf')
            if P is None:
                results['power'] = V * I
        
        elif V is not None and R is not None:
            if I is None:
                results['current'] = V / R if R != 0 else float('inf')
            if P is None:
                results['power'] = V * V / R if R != 0 else 0
        
        elif I is not None and R is not None:
            if V is None:
                results['voltage'] = I * R
            if P is None:
                results['power'] = I * I * R
        
        elif V is not None and P is not None:
            if I is None:
                results['current'] = P / V if V != 0 else 0
            if R is None:
                results['resistance'] = V * V / P if P != 0 else float('inf')
        
        elif I is not None and P is not None:
            if V is None:
                results['voltage'] = P / I if I != 0 else 0
            if R is None:
                results['resistance'] = P / (I * I) if I != 0 else float('inf')
        
        elif R is not None and P is not None:
            if V is None:
                results['voltage'] = np.sqrt(P * R)
            if I is None:
                results['current'] = np.sqrt(P / R) if R != 0 else 0
        
        return results
    
    def parallel_resistance(self, resistances: List[float]) -> float:
        """Calculate equivalent resistance of parallel resistors"""
        if not resistances:
            return float('inf')
        
        # Remove zero and infinite resistances
        valid_resistances = [r for r in resistances if r > 0 and r != float('inf')]
        
        if not valid_resistances:
            return float('inf')
        
        # 1/R_eq = 1/R1 + 1/R2 + ...
        reciprocal_sum = sum(1/r for r in valid_resistances)
        return 1/reciprocal_sum if reciprocal_sum != 0 else float('inf')
    
    def series_resistance(self, resistances: List[float]) -> float:
        """Calculate equivalent resistance of series resistors"""
        return sum(r for r in resistances if r != float('inf'))
    
    def voltage_divider(self, v_in: float, r1: float, r2: float) -> float:
        """Calculate voltage divider output: V_out = V_in * R2 / (R1 + R2)"""
        total_r = r1 + r2
        if total_r == 0:
            return 0
        return v_in * r2 / total_r
    
    def current_divider(self, i_in: float, r1: float, r2: float) -> Tuple[float, float]:
        """Calculate current division: I1 = I_in * R2/(R1+R2), I2 = I_in * R1/(R1+R2)"""
        total_r = r1 + r2
        if total_r == 0:
            return 0, 0
        i1 = i_in * r2 / total_r
        i2 = i_in * r1 / total_r
        return i1, i2
    
    def wheatstone_bridge_analysis(self, r1: float, r2: float, r3: float, r4: float, 
                                 v_supply: float) -> Dict[str, float]:
        """
        Analyze Wheatstone bridge configuration
        Returns bridge voltage and currents
        """
        # Calculate bridge voltage (voltage between middle points)
        # V_bridge = V_supply * (R3/(R1+R3) - R4/(R2+R4))
        
        if r1 + r3 == 0 or r2 + r4 == 0:
            return {'bridge_voltage': 0, 'bridge_current': 0}
        
        v_left = v_supply * r3 / (r1 + r3)
        v_right = v_supply * r4 / (r2 + r4)
        v_bridge = v_left - v_right
        
        # Calculate currents through each branch
        i_left = v_supply / (r1 + r3) if (r1 + r3) != 0 else 0
        i_right = v_supply / (r2 + r4) if (r2 + r4) != 0 else 0
        
        return {
            'bridge_voltage': v_bridge,
            'v_left': v_left,
            'v_right': v_right,
            'i_left': i_left,
            'i_right': i_right,
            'i_r1': i_left,
            'i_r2': i_right,
            'i_r3': i_left,
            'i_r4': i_right
        }
    
    def solve_circuit(self, model: CircuitModel) -> Dict[str, Dict[str, float]]:
        """
        Solve the complete H-bridge circuit
        Returns calculated values for all components
        """
        results = {}

        # Get enabled resistors and their values
        enabled = model.get_enabled_resistors()
        given_params = model.get_given_parameters()
        unknown_params = model.get_unknown_parameters()

        # Extract resistance values (convert to base units)
        resistances = {}
        for name, resistor in enabled.items():
            if resistor.resistance.is_given():
                r_value = self.converter.to_base_unit(
                    resistor.resistance.value,
                    resistor.resistance.unit,
                    'resistance'
                )
                resistances[name] = r_value
            else:
                resistances[name] = 1000.0  # Default 1kΩ for calculation

        # Get supply voltage
        v_supply = self.converter.to_base_unit(
            model.supply_voltage.value,
            model.supply_voltage.unit,
            'voltage'
        )

        # For each enabled resistor, calculate all possible parameters
        for name, resistor in enabled.items():
            if name not in results:
                results[name] = {}

            # Get known parameters for this resistor
            known = {}
            if resistor.resistance.is_given():
                known['resistance'] = self.converter.to_base_unit(
                    resistor.resistance.value, resistor.resistance.unit, 'resistance')
            if resistor.voltage.is_given():
                known['voltage'] = self.converter.to_base_unit(
                    resistor.voltage.value, resistor.voltage.unit, 'voltage')
            if resistor.current.is_given():
                known['current'] = self.converter.to_base_unit(
                    resistor.current.value, resistor.current.unit, 'current')
            if resistor.power.is_given():
                known['power'] = self.converter.to_base_unit(
                    resistor.power.value, resistor.power.unit, 'power')

            # Calculate unknown parameters using Ohm's law
            calculated = self.ohms_law_calculate(known)

            # Merge known and calculated values
            all_params = {**known, **calculated}
            results[name] = all_params

        # Perform circuit-level analysis if we have a complete bridge
        if all(name in enabled for name in ['R2', 'R3', 'R4', 'R5']):
            # Full H-bridge analysis
            bridge_results = self.wheatstone_bridge_analysis(
                resistances.get('R2', 1000),
                resistances.get('R4', 1000),
                resistances.get('R3', 1000),
                resistances.get('R5', 1000),
                v_supply
            )

            # Update results with bridge analysis
            bridge_currents = {
                'R2': bridge_results.get('i_r2', 0),
                'R3': bridge_results.get('i_r3', 0),
                'R4': bridge_results.get('i_r4', 0),
                'R5': bridge_results.get('i_r5', 0)
            }

            for name in ['R2', 'R3', 'R4', 'R5']:
                if name in enabled and name in results:
                    # Update with circuit analysis results
                    r_val = resistances[name]
                    i_val = bridge_currents[name]
                    v_val = i_val * r_val
                    p_val = v_val * i_val

                    # Only update if not already given by user
                    if not enabled[name].current.is_given():
                        results[name]['current'] = i_val
                    if not enabled[name].voltage.is_given():
                        results[name]['voltage'] = v_val
                    if not enabled[name].power.is_given():
                        results[name]['power'] = p_val

            # Add bridge measurement
            if model.meter_type == MeterType.VOLTMETER:
                results['METER'] = {
                    'measurement': bridge_results.get('bridge_voltage', 0)
                }
            else:  # Ammeter
                # Calculate bridge current (simplified)
                bridge_current = bridge_results.get('bridge_voltage', 0) / 1000.0  # Assume 1kΩ meter resistance
                results['METER'] = {
                    'measurement': bridge_current
                }

        # Handle series resistor R1 with total circuit analysis
        if 'R1' in enabled:
            total_resistance = self._calculate_total_resistance(enabled, resistances)
            total_current = v_supply / total_resistance if total_resistance != 0 else 0

            if 'R1' not in results:
                results['R1'] = {}

            # Update R1 parameters if not given by user
            if not enabled['R1'].current.is_given():
                results['R1']['current'] = total_current
            if not enabled['R1'].voltage.is_given():
                results['R1']['voltage'] = total_current * resistances['R1']
            if not enabled['R1'].power.is_given():
                results['R1']['power'] = results['R1']['voltage'] * total_current

        # Handle parallel resistor R6
        if 'R6' in enabled:
            if 'R6' not in results:
                results['R6'] = {}

            # R6 voltage is the output voltage after the bridge
            output_voltage = v_supply  # Simplified - would need more complex analysis
            if 'R1' in results:
                output_voltage -= results['R1'].get('voltage', 0)

            if not enabled['R6'].voltage.is_given():
                results['R6']['voltage'] = output_voltage
            if not enabled['R6'].current.is_given():
                results['R6']['current'] = output_voltage / resistances['R6'] if resistances['R6'] != 0 else 0
            if not enabled['R6'].power.is_given():
                results['R6']['power'] = results['R6']['voltage'] * results['R6']['current']

        return results
    
    def _calculate_total_resistance(self, enabled: Dict, resistances: Dict[str, float]) -> float:
        """Calculate total circuit resistance"""
        # This is a simplified calculation - in practice, this would need
        # more sophisticated network analysis for complex configurations
        total = 0
        
        # Add series resistance R1
        if 'R1' in enabled:
            total += resistances['R1']
        
        # Calculate bridge equivalent resistance
        bridge_resistors = ['R2', 'R3', 'R4', 'R5']
        bridge_enabled = [name for name in bridge_resistors if name in enabled]
        
        if len(bridge_enabled) >= 2:
            # Simplified bridge resistance calculation
            bridge_r = sum(resistances[name] for name in bridge_enabled) / 2
            total += bridge_r
        
        # Add parallel resistance R6
        if 'R6' in enabled and total > 0:
            r6 = resistances['R6']
            total = self.parallel_resistance([total, r6])
        
        return total
