"""
Test script for H-Bridge Analysis Application
"""

from circuit_model import CircuitModel, ParameterState, MeterType
from electrical_calculator import ElectricalCalculator

def test_basic_functionality():
    """Test basic functionality of the circuit model and calculator"""
    
    print("Testing H-Bridge Analysis Application...")
    
    # Create model and calculator
    model = CircuitModel()
    calculator = ElectricalCalculator()
    
    # Test 1: Basic circuit configuration
    print("\n1. Testing basic circuit configuration:")
    
    # Configure R1 (series resistor)
    r1 = model.get_resistor('R1')
    r1.resistance.value = 180
    r1.resistance.state = ParameterState.GIVEN
    r1.resistance.unit = 'Ω'
    
    # Configure bridge resistors (like in the reference image)
    r2 = model.get_resistor('R2')
    r2.resistance.value = 100
    r2.resistance.state = ParameterState.GIVEN
    r2.resistance.unit = 'Ω'
    
    r3 = model.get_resistor('R3')
    r3.resistance.value = 39
    r3.resistance.state = ParameterState.GIVEN
    r3.resistance.unit = 'Ω'
    
    r4 = model.get_resistor('R4')
    r4.resistance.value = 22
    r4.resistance.state = ParameterState.GIVEN
    r4.resistance.unit = 'Ω'
    
    # R5 is disabled (R5 = 0 in reference)
    r5 = model.get_resistor('R5')
    r5.enabled = False
    
    r6 = model.get_resistor('R6')
    r6.resistance.value = 39
    r6.resistance.state = ParameterState.GIVEN
    r6.resistance.unit = 'Ω'
    
    # Set supply voltage
    model.supply_voltage.value = 1.0
    model.supply_voltage.unit = 'V'
    
    print(f"Supply voltage: {model.supply_voltage.value} {model.supply_voltage.unit}")
    print(f"R1: {r1.resistance.value} {r1.resistance.unit}")
    print(f"R2: {r2.resistance.value} {r2.resistance.unit}")
    print(f"R3: {r3.resistance.value} {r3.resistance.unit}")
    print(f"R4: {r4.resistance.value} {r4.resistance.unit}")
    print(f"R5: {'Disabled' if not r5.enabled else f'{r5.resistance.value} {r5.resistance.unit}'}")
    print(f"R6: {r6.resistance.value} {r6.resistance.unit}")
    
    # Test 2: Circuit validation
    print("\n2. Testing circuit validation:")
    valid, message = model.validate_configuration()
    print(f"Configuration valid: {valid}")
    if not valid:
        print(f"Validation message: {message}")
    
    # Test 3: Basic calculations
    print("\n3. Testing basic calculations:")
    try:
        results = calculator.solve_circuit(model)
        print("Calculation results:")
        for component, params in results.items():
            print(f"\n{component}:")
            for param_name, value in params.items():
                if isinstance(value, float):
                    print(f"  {param_name}: {value:.6f}")
                else:
                    print(f"  {param_name}: {value}")
    except Exception as e:
        print(f"Error in calculations: {e}")
    
    # Test 4: Unit conversion
    print("\n4. Testing unit conversion:")
    converter = calculator.converter
    
    # Test resistance conversion
    r_ohm = 1000
    r_kohm = converter.from_base_unit(r_ohm, 'kΩ', 'resistance')
    print(f"{r_ohm} Ω = {r_kohm} kΩ")
    
    # Test voltage conversion
    v_volt = 1.0
    v_mv = converter.from_base_unit(v_volt, 'mV', 'voltage')
    print(f"{v_volt} V = {v_mv} mV")
    
    # Test current conversion
    i_amp = 0.001
    i_ma = converter.from_base_unit(i_amp, 'mA', 'current')
    print(f"{i_amp} A = {i_ma} mA")
    
    # Test 5: Enhanced parameter calculations
    print("\n5. Testing enhanced parameter calculations:")
    known_params = {'voltage': 5.0, 'resistance': 1000.0}
    calculated = calculator.solve_all_parameters(known_params)
    print(f"Given: V={known_params['voltage']}V, R={known_params['resistance']}Ω")
    print(f"Calculated: {calculated}")

    # Test with different combinations
    print("\nTesting with current and power:")
    known_params2 = {'current': 0.01, 'power': 0.1}
    calculated2 = calculator.solve_all_parameters(known_params2)
    print(f"Given: I={known_params2['current']}A, P={known_params2['power']}W")
    print(f"Calculated: {calculated2}")
    
    # Test 6: Wheatstone bridge analysis
    print("\n6. Testing Wheatstone bridge analysis:")
    bridge_results = calculator.wheatstone_bridge_analysis(100, 22, 39, 39, 1.0)
    print("Bridge analysis results:")
    for key, value in bridge_results.items():
        print(f"  {key}: {value:.6f}")
    
    print("\nAll tests completed!")

if __name__ == "__main__":
    test_basic_functionality()
